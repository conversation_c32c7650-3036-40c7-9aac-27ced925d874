@extends('backend.layout')

{{-- this style will be applied when the direction of language is right-to-left --}}
@includeIf('backend.partials.rtl_style')

@section('content')
  <div class="page-header">
    <h4 class="page-title">{{ __('Property') }}</h4>
    <ul class="breadcrumbs">
      <li class="nav-home">
        <a href="{{ route('admin.dashboard') }}">
          <i class="flaticon-home"></i>
        </a>
      </li>
      <li class="separator">
        <i class="flaticon-right-arrow"></i>
      </li>
      <li class="nav-item">
        <a href="#">{{ __('Property Management') }}</a>
      </li>
      <li class="separator">
        <i class="flaticon-right-arrow"></i>
      </li>
      <li class="nav-item">
        <a href="#">{{ __('Property') }}</a>
      </li>
    </ul>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <div class="row">
            <div class="col-lg-3">
              <div class="card-title d-inline-block">{{ __('Property') }}</div>
            </div>

            <div class="col-md-6">
              <form id="searchForm" action="{{ route('admin.properties_management.properties') }}" method="GET">
                <input type="hidden" name="language" value="{{ $defaultLang->code }}">

                <div class="row">
                  <div class="col-lg-6">
                    <div class="form-group">
                      <select class="form-control select2" name="vendor"
                        onchange="document.getElementById('searchForm').submit()">
                        <option value="">{{ __('All') }}</option>
                        <option {{ request()->input('vendor') == 'admin' ? 'selected' : '' }} value="admin">
                          {{ __('Admin') }}
                        </option>
                        @foreach ($vendors as $item)
                          <option value="{{ $item->id }}"
                            {{ request()->input('vendor') == $item->id ? 'selected' : '' }}>
                            {{ $item->username }}</option>
                        @endforeach
                      </select>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <div class="form-group">
                      <input type="text" name="title" class="form-control" placeholder="Title"
                        value="{{ request()->input('title') }}">
                    </div>
                  </div>
                </div>

              </form>
            </div>

            <div class="col-lg-3 mt-2 mt-lg-0">
              <a href="{{ route('admin.properties_management.create_property') }}"
                class="btn btn-primary btn-sm float-lg-right float-left"><i class="fas fa-plus"></i>
                {{ __('Add Property') }}</a>

              <button class="btn btn-danger btn-sm float-lg-right float-right mr-2 d-none bulk-delete"
                data-href="{{ route('admin.properties_management.bulk_delete_property') }}"><i
                  class="flaticon-interface-5"></i> {{ __('Delete') }}</button>
            </div>
          </div>
        </div>

        <div class="card-body">
          <div class="row">
            <div class="col-lg-12">
              @if (count($properties) == 0)
                <h3 class="text-center">{{ __('NO PROPERTY FOUND!') }}</h3>
              @else
                <div class="table-responsive">
                  <table class="table table-striped mt-3">
                    <thead>
                      <tr>
                        <th scope="col">
                          <input type="checkbox" class="bulk-check" data-val="all">
                        </th>
                        <th scope="col">{{ __('Title') }}</th>
                        <th scope="col">{{ __('Landlord') }}</th>
                        <th scope="col">{{ __('Price') }}</th>
                        <th scope="col">{{ __('Locations') }}</th>
                        <th scope="col">{{ __('Plans') }}</th>
                        <th scope="col">{{ __('Featured') }}</th>
                        <th scope="col">{{ __('Actions') }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach ($properties as $property)
                        <tr>
                          <td>
                            <input type="checkbox" class="bulk-check" data-val="{{ $property->id }}">
                          </td>
                          <td>
                            @if($property->property_content)
                            <a target="_blank"
                              href="{{ route('property_details', ['id' => $property->id, 'slug' => $property->property_content->slug]) }}">{{ strlen($property->property_content->title) > 25 ? mb_substr($property->property_content->title, 0, 25, 'utf-8') . '...' : $property->property_content->title }}</a>
                              @endif
                          </td>
                          <td>
                            @if ($property->vendor_id)
                              <a
                                href="{{ route('admin.vendor_management.vendor_details', ['id' => $property->vendor_id, 'language' => $defaultLang->code]) }}">{{ $vendor = optional($property->vendor)->username }}</a>
                            @else
                              <a href="javascript:void(0)">{{ __('Admin') }}</a>
                            @endif
                          </td>
                          <td>
                            @if ($property->pricing_type == 'negotiable')
                              {{ __('Negotiable') }}
                            @else
                              {{ $currencyInfo->base_currency_symbol_position == 'left' ? $currencyInfo->base_currency_symbol : '' }}
                              {{ $property->property_price }}
                              {{ $currencyInfo->base_currency_symbol_position == 'right' ? $currencyInfo->base_currency_symbol : '' }}
                              <span class="text-capitalize">{{ '(' . $property->pricing_type . ')' }}</span>
                            @endif
                          </td>
                          <td>
                            <a class="btn btn-primary btn-sm"
                              href="{{ route('admin.properties_management.view_locations', ['property_id' => $property->id, 'language' => $defaultLang->code]) }}"
                              target="_blank">{{ __('Manage') }}</a>
                          </td>
                          <td>
                            <a class="btn btn-primary btn-sm"
                              href="{{ route('admin.properties_management.view_plans', ['property_id' => $property->id, 'language' => $defaultLang->code]) }}"
                              target="_blank">{{ __('Manage') }}</a>
                          </td>
                          <td>
                            <form id="featureForm{{ $property->id }}" class="d-inline-block"
                              action="{{ route('admin.properties_management.update_featured_property') }}" method="post">
                              @csrf
                              <input type="hidden" name="propertyId" value="{{ $property->id }}">

                              <select
                                class="form-control {{ $property->is_featured == 1 ? 'bg-success' : 'bg-danger' }} form-control-sm"
                                name="is_featured"
                                onchange="document.getElementById('featureForm{{ $property->id }}').submit();">
                                <option value="1" {{ $property->is_featured == 1 ? 'selected' : '' }}>
                                  {{ __('Yes') }}
                                </option>
                                <option value="0" {{ $property->is_featured == 0 ? 'selected' : '' }}>
                                  {{ __('No') }}
                                </option>
                              </select>
                            </form>
                          </td>
                          <td>
                            <a class="btn btn-secondary btn-sm mr-1 mb-1"
                              href="{{ route('admin.properties_management.edit_property', $property->id) }}">
                              <span class="btn-label">
                                <i class="fas fa-edit mr__3"></i>
                              </span>
                            </a>

                            <form class="deleteForm d-inline-block"
                              action="{{ route('admin.properties_management.delete_property') }}" method="post">
                              @csrf
                              <input type="hidden" name="property_id" value="{{ $property->id }}">

                              <button type="submit" class="btn btn-danger btn-sm mb-1 deleteBtn">
                                <span class="btn-label">
                                  <i class="fas fa-trash mr__3"></i>
                                </span>
                              </button>
                            </form>
                          </td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @endif
            </div>
          </div>
        </div>
        <div class="card-footer">
          {{ $properties->appends([
                  'vendor' => request()->input('vendor'),
              ])->links() }}
        </div>
      </div>
    </div>
  </div>

@endsection
