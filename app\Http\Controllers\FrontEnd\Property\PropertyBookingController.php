<?php

namespace App\Http\Controllers\FrontEnd\Property;

use App\Http\Controllers\Controller;
use App\Http\Controllers\FrontEnd\Property\FlutterwaveController;
use App\Http\Controllers\FrontEnd\Property\InstamojoController;
use App\Http\Controllers\FrontEnd\Property\MercadoPagoController;
use App\Http\Controllers\FrontEnd\Property\MollieController;
use App\Http\Controllers\FrontEnd\Property\OfflineController;
use App\Http\Controllers\FrontEnd\Property\PayPalController;
use App\Http\Controllers\FrontEnd\Property\PaystackController;
use App\Http\Controllers\FrontEnd\Property\PaytmController;
use App\Http\Controllers\FrontEnd\Property\RazorpayController;
use App\Http\Controllers\FrontEnd\Property\StripeController;
use App\Models\BasicSettings\MailTemplate;
use App\Models\Commission;
use App\Models\PropertyManagement\Coupon;
use App\Models\PropertyManagement\Property;
use App\Models\PropertyManagement\PropertyBooking;
use App\Traits\MiscellaneousTrait;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;

class PropertyBookingController extends Controller
{
  use MiscellaneousTrait;

  public function makePropertyBooking(Request $request)
  {
    $property = Property::where('id', $request->property_id)->first();
    $maxPersons = $property->max_persons;

    // validation starts
    $rules = [
      'customer_name' => 'required',
      'customer_phone' => 'required',
      'customer_email' => 'required|email:rfc,dns',
      'visitors' => [
        'required',
        'numeric',
        function ($attribute, $value, $fail) use ($maxPersons) {
          if (!is_null($maxPersons) && $value > $maxPersons) {
            $fail('Number of visitors cannot be more than ' . $maxPersons . '.');
          }
        }
      ],
      'gateway' => 'required'
    ];

    $validator = Validator::make($request->all(), $rules);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }
    // validation ends

    // get the currency information from db
    $currencyInfo = MiscellaneousTrait::getCurrencyInfo();

    // checking whether the currency is set to 'INR' or not
    if ($currencyInfo->base_currency_text !== 'INR') {
      return redirect()->back()->with('error', 'Invalid currency for Razorpay payment.')->withInput();
    }

    $property = Property::find($request->property_id);

    $subtotal = $property->property_price * $request->visitors;

    $discount = 0;

    if ($request->filled('coupon')) {
      $coupon = Coupon::where('code', $request->coupon)->first();

      if (!empty($coupon)) {
        if ($coupon->type == 'fixed') {
          $discount = $coupon->value;
        } else {
          $discount = ($subtotal * $coupon->value) / 100;
        }
      }
    }

    $total = $subtotal - $discount;

    // generate an unique booking number
    $bookingNumber = uniqid();

    $bookingInfo = [
      'booking_number' => $bookingNumber,
      'user_id' => Auth::guard('web')->check() == true ? Auth::guard('web')->user()->id : null,
      'vendor_id' => $property->vendor_id,
      'customer_name' => $request->customer_name,
      'customer_email' => $request->customer_email,
      'customer_phone' => $request->customer_phone,
      'property_id' => $request->property_id,
      'visitors' => $request->visitors,
      'subtotal' => $subtotal,
      'discount' => $discount,
      'grand_total' => $total,
      'currency_symbol' => $currencyInfo->base_currency_symbol,
      'currency_symbol_position' => $currencyInfo->base_currency_symbol_position,
      'currency_text' => $currencyInfo->base_currency_text,
      'currency_text_position' => $currencyInfo->base_currency_text_position,
      'payment_method' => $request->gateway,
      'gateway_type' => 'online',
      'payment_status' => 'pending'
    ];

    $title = 'Property Booking';
    $notifyURL = route('property_booking.razorpay.notify');

    $razorpayInfo = DB::table('payment_gateways')->where('keyword', 'razorpay')->first();
    $razorpayData = json_decode($razorpayInfo->information, true);

    Config::set('services.razorpay.key', $razorpayData['key']);
    Config::set('services.razorpay.secret', $razorpayData['secret']);

    Session::put('propertyBookingInfo', $bookingInfo);
    Session::put('propertyId', $request->property_id);

    $api = new Api($razorpayData['key'], $razorpayData['secret']);

    $orderData = [
      'receipt' => $bookingNumber,
      'amount' => $total * 100,
      'currency' => 'INR'
    ];

    $razorpayOrder = $api->order->create($orderData);

    $information['title'] = $title;
    $information['notifyURL'] = $notifyURL;
    $information['total'] = $total;
    $information['razorpay_order_id'] = $razorpayOrder['id'];
    $information['razorpay_merchant_key'] = $razorpayData['key'];
    $information['customer_name'] = $request->customer_name;
    $information['customer_email'] = $request->customer_email;
    $information['customer_phone'] = $request->customer_phone;

    return view('frontend.payment-gateways.razorpay', $information);
  }

  public function storePropertyBooking(Request $request)
  {
    $bookingInfo = Session::get('propertyBookingInfo');

    $booking = PropertyBooking::create($bookingInfo);

    Session::forget('propertyBookingInfo');
    Session::forget('propertyId');

    // generate an invoice in pdf format
    $invoice = $this->generateInvoice($booking);

    // then, update the invoice field info in database
    $booking->update(['invoice' => $invoice]);

    // send a mail to the customer with the invoice
    $this->sendMail($booking);

    return redirect()->route('property_booking.complete', ['id' => $booking->id]);
  }

  public function complete($id)
  {
    $booking = PropertyBooking::find($id);

    return view('frontend.properties.booking-complete', compact('booking'));
  }

  private function generateInvoice($booking)
  {
    $fileName = $booking->booking_number . '.pdf';
    $directory = public_path('assets/file/invoices/property/');
    @mkdir($directory, 0775, true);

    $currencyInfo = MiscellaneousTrait::getCurrencyInfo();

    // get property title
    $language = MiscellaneousTrait::getLanguage();
    $propertyInfo = $booking->property()->first();
    $title = $propertyInfo->property_content()->where('language_id', $language->id)->pluck('title')->first();

    $data['booking'] = $booking;
    $data['service'] = $title;
    $data['currencyInfo'] = $currencyInfo;

    $pdf = PDF::loadView('frontend.properties.invoice', $data)->save($directory . $fileName);

    return $fileName;
  }

  private function sendMail($booking)
  {
    // first, get the mail template information from db
    $mailTemplate = MailTemplate::where('mail_type', 'property_booking')->first();
    $mailSubject = $mailTemplate->mail_subject;
    $mailBody = $mailTemplate->mail_body;

    // second, send a mail to the customer
    $websiteInfo = DB::table('basic_settings')->select('website_title', 'smtp_status', 'smtp_host', 'smtp_port', 'encryption', 'smtp_username', 'smtp_password', 'from_mail', 'from_name')->where('uniqid', 12345)->first();

    $currencyInfo = MiscellaneousTrait::getCurrencyInfo();

    // get property title
    $language = MiscellaneousTrait::getLanguage();
    $propertyInfo = $booking->property()->first();
    $title = $propertyInfo->property_content()->where('language_id', $language->id)->pluck('title')->first();

    $mailBody = str_replace('{customer_name}', $booking->customer_name, $mailBody);
    $mailBody = str_replace('{booking_number}', $booking->booking_number, $mailBody);
    $mailBody = str_replace('{property_title}', $title, $mailBody);
    $mailBody = str_replace('{website_title}', $websiteInfo->website_title, $mailBody);

    // configure the mail info
    $configData = [
      'mail_driver' => 'smtp',
      'mail_host' => $websiteInfo->smtp_host,
      'mail_port' => $websiteInfo->smtp_port,
      'mail_username' => $websiteInfo->smtp_username,
      'mail_password' => $websiteInfo->smtp_password,
      'mail_encryption' => $websiteInfo->encryption,
      'mail_from_address' => $websiteInfo->from_mail,
      'mail_from_name' => $websiteInfo->from_name
    ];

    Config::set('mail', $configData);

    // send a mail to the customer
    Mail::send([], [], function (Message $message) use ($mailSubject, $mailBody, $booking) {
      $message->to($booking->customer_email)
        ->subject($mailSubject)
        ->html($mailBody, 'text/html')
        ->attach(public_path('assets/file/invoices/property/' . $booking->invoice));
    });
  }

  // Legacy method names for backward compatibility
  public function makePropertyBooking(Request $request)
  {
    return $this->makePropertyBooking($request);
  }
}
