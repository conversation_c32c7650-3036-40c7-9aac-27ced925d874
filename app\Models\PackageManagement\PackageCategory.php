<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'language_id',
        'name',
        'status',
        'serial_number',
    ];

    public function PropertyCategoryLang()
    {
        return $this->belongsTo('App\Models\Language');
    }

    public function PropertyContentList()
    {
        return $this->hasMany('App\Models\PropertyManagement\PropertyContent');
    }
}
