<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Use raw SQL to rename columns for better compatibility with MariaDB/MySQL

        // Rename package-related columns to property-related columns in basic_settings table
        if (Schema::hasColumn('basic_settings', 'package_category_status')) {
            DB::statement('ALTER TABLE basic_settings CHANGE package_category_status property_category_status TINYINT(3) UNSIGNED NOT NULL');
        }
        if (Schema::hasColumn('basic_settings', 'package_rating_status')) {
            DB::statement('ALTER TABLE basic_settings CHANGE package_rating_status property_rating_status TINYINT(3) UNSIGNED NOT NULL');
        }
        if (Schema::hasColumn('basic_settings', 'package_guest_checkout_status')) {
            DB::statement('ALTER TABLE basic_settings CHANGE package_guest_checkout_status property_guest_checkout_status TINYINT(3) UNSIGNED NOT NULL');
        }

        // Update page_headings table to rename packages_title to properties_title
        if (Schema::hasColumn('page_headings', 'packages_title')) {
            DB::statement('ALTER TABLE page_headings CHANGE packages_title properties_title VARCHAR(255)');
        }

        // Update SEO table if it has package-related columns
        if (Schema::hasTable('seos')) {
            if (Schema::hasColumn('seos', 'packages_meta_keywords')) {
                DB::statement('ALTER TABLE seos CHANGE packages_meta_keywords properties_meta_keywords TEXT');
            }
            if (Schema::hasColumn('seos', 'packages_meta_description')) {
                DB::statement('ALTER TABLE seos CHANGE packages_meta_description properties_meta_description TEXT');
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Reverse the column renames using raw SQL

        // Reverse the column renames in basic_settings table
        if (Schema::hasColumn('basic_settings', 'property_category_status')) {
            DB::statement('ALTER TABLE basic_settings CHANGE property_category_status package_category_status TINYINT(3) UNSIGNED NOT NULL');
        }
        if (Schema::hasColumn('basic_settings', 'property_rating_status')) {
            DB::statement('ALTER TABLE basic_settings CHANGE property_rating_status package_rating_status TINYINT(3) UNSIGNED NOT NULL');
        }
        if (Schema::hasColumn('basic_settings', 'property_guest_checkout_status')) {
            DB::statement('ALTER TABLE basic_settings CHANGE property_guest_checkout_status package_guest_checkout_status TINYINT(3) UNSIGNED NOT NULL');
        }

        // Reverse page_headings table changes
        if (Schema::hasColumn('page_headings', 'properties_title')) {
            DB::statement('ALTER TABLE page_headings CHANGE properties_title packages_title VARCHAR(255)');
        }

        // Reverse SEO table changes if it exists
        if (Schema::hasTable('seos')) {
            if (Schema::hasColumn('seos', 'properties_meta_keywords')) {
                DB::statement('ALTER TABLE seos CHANGE properties_meta_keywords packages_meta_keywords TEXT');
            }
            if (Schema::hasColumn('seos', 'properties_meta_description')) {
                DB::statement('ALTER TABLE seos CHANGE properties_meta_description packages_meta_description TEXT');
            }
        }
    }
};
