@extends('vendors.layout')

@section('content')
  <div class="page-header">
    <h4 class="page-title">{{ __('Rental Details') }}</h4>
    <ul class="breadcrumbs">
      <li class="nav-home">
        <a href="{{ route('vendor.dashboard') }}">
          <i class="flaticon-home"></i>
        </a>
      </li>
      <li class="separator">
        <i class="flaticon-right-arrow"></i>
      </li>
      <li class="nav-item">
        <a href="#">{{ __('Property Management') }}</a>
      </li>
      <li class="separator">
        <i class="flaticon-right-arrow"></i>
      </li>
      <li class="nav-item">
        <a href="#">{{ __('Property Rentals') }}</a>
      </li>
      <li class="separator">
        <i class="flaticon-right-arrow"></i>
      </li>
      <li class="nav-item">
        <a href="#">{{ __('Rental Details') }}</a>
      </li>
    </ul>
  </div>

  <div class="row">
    <div class="col-md-7">
      <div class="card">
        <div class="card-header">
          <div class="card-title d-inline-block">{{ __('Rental Id') . ' #' }} {{ $details->booking_number }}</div>
          <a class="btn btn-info btn-sm float-right d-inline-block"
            href="{{ route('vendor.property_bookings.all_bookings') }}">
            <span class="btn-label">
              <i class="fas fa-backward"></i>
            </span>
            {{ __('Back') }}
          </a>
        </div>

        @php
          $position = $details->currency_text_position;
          $currency = $details->currency_text;
        @endphp

        <div class="card-body">
          <div class="payment-information">


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('rental date') . ' :' }}</strong>
              </div>
              <div class="col-lg-8">
                {{ date_format($details->created_at, 'F d, Y') }}
              </div>
            </div>

            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('property name :') }}</strong>
              </div>
              @php
                $propertyInfo = $details->tourProperty->PropertyContent->where('language_id', $defaultLang->id)->first();
              @endphp

              <div class="col-lg-8">
                @if ($propertyInfo)
                  <a href="{{ route('property_details', ['id' => $propertyInfo->property_id, 'slug' => $propertyInfo->slug]) }}"
                    target="_blank">{{ strlen($propertyInfo->title) > 25 ? mb_substr($propertyInfo->title, 0, 25, 'utf-8') . '...' : $propertyInfo->title }}</a>
                @endif

              </div>
            </div>


            @if ($PropertyCategoryName != null)
              <div class="row">
                <div class="col-lg-4">
                  <strong class="text-capitalize">{{ __('roperty type :') }}</strong>
                </div>
                <div class="col-lg-8">{{ $PropertyCategoryName }}</div>
              </div>
            @endif

            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('number of persons :') }}</strong>
              </div>
              <div class="col-lg-8">{{ $details->visitors }}</div>
            </div>


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('subtotal :') }}</strong>
              </div>
              <div class="col-lg-8">
                {{ $position == 'left' ? $currency . ' ' : '' }}{{ $details->subtotal }}{{ $position == 'right' ? ' ' . $currency : '' }}
              </div>
            </div>


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('discount :') }}</strong>
              </div>
              <div class="col-lg-8">
                {{ $position == 'left' ? $currency . ' ' : '' }}{{ $details->discount }}{{ $position == 'right' ? ' ' . $currency : '' }}
              </div>
            </div>


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('Tenant Paid') . ' : ' }}</strong>
              </div>
              <div class="col-lg-8">
                {{ $position == 'left' ? $currency . ' ' : '' }}{{ $details->grand_total }}{{ $position == 'right' ? ' ' . $currency : '' }}
              </div>
            </div>

            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('Received Amount') . ' : ' }}</strong>
              </div>
              <div class="col-lg-8">
                {{ $position == 'left' ? $currency . ' ' : '' }}{{ $details->received_amount }}{{ $position == 'right' ? ' ' . $currency : '' }}
              </div>
            </div>

            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('Commission') . ' : ' }}</strong>
              </div>
              <div class="col-lg-8">
                {{ $position == 'left' ? $currency . ' ' : '' }}{{ $details->comission }}{{ $position == 'right' ? ' ' . $currency : '' }}
                ({{ __('Received by Admin') }})
              </div>
            </div>


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('payment method :') }}</strong>
              </div>
              <div class="col-lg-8">{{ $details->payment_method }}</div>
            </div>


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('payment status :') }}</strong>
              </div>
              <div class="col-lg-8">
                {{ $details->payment_status == 1 ? 'Paid' : 'Unpaid' }}
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <div class="col-md-5">
      <div class="card">
        <div class="card-header">
          <div class="card-title d-inline-block">{{ __('Tenant Information') }}</div>
        </div>
        <div class="card-body">
          <div class="payment-information">
            @if (!is_null($details->user_id))
              <div class="row">
                <div class="col-lg-4">
                  <strong class="text-capitalize">{{ __('fullname') . ' : ' }}</strong>
                </div>
                <div class="col-lg-8"><a
                    href="{{ route('register.user.view', $details->user_id) }}">{{ @$details->user->username }}</a>
                </div>
              </div>
            @endif

            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('name :') }}</strong>
              </div>
              <div class="col-lg-8">{{ convertUtf8($details->customer_name) }}</div>
            </div>


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('email :') }}</strong>
              </div>
              <div class="col-lg-8"> {{ $details->customer_email }}</div>
            </div>


            <div class="row">
              <div class="col-lg-4">
                <strong class="text-capitalize">{{ __('phone : ') }}</strong>
              </div>
              <div class="col-lg-8"> {{ $details->customer_phone }}</div>
            </div>

            @if (!is_null($details->user_id))
              <div class="row">
                <div class="col-lg-4">
                  <strong class="text-capitalize">{{ __('Address') . ' : ' }}</strong>
                </div>
                <div class="col-lg-8"> {{ @$details->user->address }}</div>
              </div>

              <div class="row">
                <div class="col-lg-4">
                  <strong class="text-capitalize">{{ __('City') . ' : ' }}</strong>
                </div>
                <div class="col-lg-8"> {{ @$details->user->city }}</div>
              </div>

              <div class="row">
                <div class="col-lg-4">
                  <strong class="text-capitalize">{{ __('Province') . ' : ' }}</strong>
                </div>
                <div class="col-lg-8"> {{ @$details->user->state }}</div>
              </div>

              <div class="row">
                <div class="col-lg-4">
                  <strong class="text-capitalize">{{ __('Country') . ' : ' }}</strong>
                </div>
                <div class="col-lg-8"> {{ @$details->user->country }}</div>
              </div>
            @endif

          </div>
        </div>

      </div>
    </div>
  </div>
@endsection
