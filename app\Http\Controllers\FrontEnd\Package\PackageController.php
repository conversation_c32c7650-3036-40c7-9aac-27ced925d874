<?php

namespace App\Http\Controllers\FrontEnd\property;

use App\Http\Controllers\Controller;
use App\Models\PropertyManagement\Coupon;
use App\Models\PropertyManagement\property;
use App\Models\PropertyManagement\PropertyBooking;
use App\Models\PropertyManagement\PropertyCategory;
use App\Models\PropertyManagement\PropertyContent;
use App\Models\PropertyManagement\PropertyLocation;
use App\Models\PropertyManagement\PropertyPlan;
use App\Models\PropertyManagement\PropertyReview;
use App\Models\PaymentGateway\OfflineGateway;
use App\Models\PaymentGateway\OnlineGateway;
use App\Models\RoomManagement\Room;
use App\Models\Vendor;
use App\Traits\MiscellaneousTrait;
use Carbon\Carbon;
use Config;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PropertyController extends Controller
{
  use MiscellaneousTrait;

  public function properties(Request $request)
  {
    $queryResult['breadcrumbInfo'] = MiscellaneousTrait::getBreadcrumb();
    $queryResult['propertyRating'] = DB::table('basic_settings')->select('property_rating_status')->first();

    $language = MiscellaneousTrait::getLanguage();

    $queryResult['categories'] = PropertyCategory::where('language_id', $language->id)->where('status', 1)->orderBy('serial_number', 'ASC')->get();

    $queryResult['pageHeading'] = MiscellaneousTrait::getPageHeading($language);

    $queryResult['maxPrice'] = property::max('property_price');

    $queryResult['minPrice'] = property::min('property_price');
    $queryResult['maxPersons'] = property::max('max_persons');
    $queryResult['maxDays'] = property::max('number_of_days');

    $property_name = $sort_value = $location_name = $min_price = $max_price = null;

    $category = $request->category;
    if ($request->filled('propertyName')) {
      $property_name = $request->propertyName;
    }
    if ($request->filled('personsValue')) {
      $persons_value = $request->personsValue;
    } else {
      $persons_value = 0;
    }
    if ($request->filled('daysValue')) {
      $days_value = $request->daysValue;
    } else {
      $days_value = 0;
    }
    $sort_value = $request->sortValue;
    $true = true;
    $location_name = $request->locationName;
    if ($request->filled('minPrice') && $request->filled('maxPrice')) {
      $min_price = $request->minPrice;
      $max_price = $request->maxPrice;
    }

    $propertyIds = [];
    if (!empty($location_name)) {
      $locations = PropertyLocation::select('property_id')->where('name', 'LIKE', '%' . $location_name . '%')->get();
      foreach ($locations as $key => $location) {
        if (!in_array($location->property_id, $propertyIds)) {
          $propertyIds[] = $location->property_id;
        }
      }
    }

    $propertyInfos = property::join('property_contents', 'properties.id', '=', 'property_contents.property_id')
      ->where('property_contents.language_id', '=', $language->id)
      ->when($category, function ($query, $category) {
        return $query->where('property_contents.property_category_id', $category);
      })
      ->when($property_name, function ($query, $property_name) {
        return $query->where('title', 'like', '%' . $property_name . '%');
      })->when(($min_price && $max_price), function ($query) use ($min_price, $max_price) {
        return $query->where('property_price', '>=', $min_price)->where('property_price', '<=', $max_price);
      })->when($days_value, function ($query, $days_value) {
        return $query->where('number_of_days', '<=', $days_value);
      })->when($persons_value, function ($query, $persons_value) {
        return $query->where('max_persons', '>=', $persons_value);
      })->when($location_name, function ($query) use ($propertyIds) {
        return $query->whereIn('properties.id', $propertyIds);
      });


    if ($sort_value == 'new-properties') {
      $propertyInfos->orderBy('properties.created_at', 'desc');
    } else if ($sort_value == 'old-properties') {
      $propertyInfos->orderBy('properties.created_at', 'asc');
    } else if ($sort_value == 'price-asc') {
      $propertyInfos->orderBy('properties.property_price', 'asc');
    } else if ($sort_value == 'price-desc') {
      $propertyInfos->orderBy('properties.property_price', 'desc');
    } else if ($sort_value == 'max-persons-asc') {
      $propertyInfos->orderBy('properties.max_persons', 'asc');
    } else if ($sort_value == 'max-persons-desc') {
      $propertyInfos->orderBy('properties.max_persons', 'desc');
    } else if ($sort_value == 'days-asc') {
      $propertyInfos->orderBy('properties.number_of_days', 'asc');
    } else if ($sort_value == 'days-desc') {
      $propertyInfos->orderBy('properties.number_of_days', 'desc');
    } else if ($request->filled('number_of_days')) {
      $propertyInfos->orderBy('properties.number_of_days', 'desc');
    } else {
      $propertyInfos->orderByDesc('properties.id');
    }

    $queryResult['propertyInfos'] = $propertyInfos->paginate(6);

    $queryResult['currencyInfo'] = MiscellaneousTrait::getCurrencyInfo();

    return view('frontend.properties.properties', $queryResult);
  }

  public function propertyDetails($id)
  {
    $queryResult['breadcrumbInfo'] = MiscellaneousTrait::getBreadcrumb();
    $queryResult['propertyRating'] = DB::table('basic_settings')->select('property_rating_status')->first();

    $language = MiscellaneousTrait::getLanguage();

    $details = PropertyContent::with('property')
      ->where('language_id', $language->id)
      ->where('property_id', $id)
      ->firstOrFail();

    $queryResult['details'] = $details;

    $queryResult['plans'] = PropertyPlan::where('language_id', $language->id)
      ->where('property_id', $id)
      ->get();

    $queryResult['locations'] = PropertyLocation::where('language_id', $language->id)
      ->where('property_id', $id)
      ->get();

    $queryResult['reviews'] = PropertyReview::where('property_id', $id)->orderBy('id', 'DESC')->get();

    $queryResult['status'] = DB::table('basic_settings')
      ->select('property_rating_status', 'property_guest_checkout_status')
      ->where('uniqid', '=', 12345)
      ->first();

    $queryResult['onlineGateways'] = OnlineGateway::where('status', 1)->get();

    $queryResult['offlineGateways'] = OfflineGateway::where('status', 1)->orderBy('serial_number', 'asc')->get()->map(function ($gateway) {
      return [
        'id' => $gateway->id,
        'name' => $gateway->name,
        'short_description' => $gateway->short_description,
        'instructions' => replaceBaseUrl($gateway->instructions, 'summernote'),
        'attachment_status' => $gateway->attachment_status,
        'serial_number' => $gateway->serial_number
      ];
    });

    $queryResult['currencyInfo'] = MiscellaneousTrait::getCurrencyInfo();

    $queryResult['latestPackages'] = PropertyContent::with('property')
      ->where('language_id', $language->id)
      ->where('property_category_id', $details->property_category_id)
      ->where('property_id', '<>', $details->property_id)
      ->orderBy('property_id', 'desc')
      ->limit(3)
      ->get();

    $queryResult['avgRating'] = PropertyReview::where('property_id', $id)->avg('rating');
    $stripe = OnlineGateway::query()->whereKeyword('stripe')->first();
    if ($stripe) {
      $stripeInformation = json_decode($stripe->information, true);
      $queryResult['stripeKey'] = $stripeInformation['key'];
    } else {
      $queryResult['stripeKey'] = null;
    }

    return view('frontend.properties.property_details', $queryResult);
  }

  public function applyCoupon(Request $request)
  {
    try {
      $coupon = Coupon::where('code', $request->coupon)->firstOrFail();

      $startDate = Carbon::parse($coupon->start_date);
      $endDate = Carbon::parse($coupon->end_date);
      $todayDate = Carbon::now();

      // check coupon is valid or not
      if ($todayDate->between($startDate, $endDate) == false) {
        return response()->json(['error' => 'Sorry, coupon has been expired!']);
      }

      // check coupon is valid or not for this property
      $propertyId = $request->propertyId;
      $propertyIds = empty($coupon->properties) ? '' : json_decode($coupon->properties);

      if (!empty($propertyIds) && !in_array($propertyId, $propertyIds)) {
        return response()->json(['error' => 'You can not apply this coupon for this property!']);
      }

      session()->put('couponCode', $request->coupon);

      $initTotalRent = str_replace(',', '', $request->initTotal);

      if ($coupon->type == 'fixed') {
        $total = floatval($initTotalRent) - floatval($coupon->value);

        return response()->json([
          'success' => 'Coupon applied successfully.',
          'discount' => $coupon->value,
          'total' => $total,
        ]);
      } else {
        $initTotalRent = floatval($initTotalRent);
        $couponVal = floatval($coupon->value);

        $discount = $initTotalRent * ($couponVal / 100);
        $total = $initTotalRent - $discount;

        return response()->json([
          'success' => 'Coupon applied successfully.',
          'discount' => $discount,
          'total' => $total
        ]);
      }
    } catch (ModelNotFoundException $e) {
      return response()->json(['error' => 'Coupon is not valid!']);
    }
  }

  public function removeCoupon()
  {
    session()->forget('couponCode');
  }

  public function storeReview(Request $request, $id)
  {
    $booking = PropertyBooking::where('user_id', Auth::user()->id)->where('property_id', $id)->where('payment_status', 1)->count();
    if ($booking == 0) {
      session()->flash('error', "You had not purchased this property yet.");
      return back();
    }

    $rules = ['rating' => 'required|numeric'];

    $message = [
      'rating.required' => 'The star rating field is required.'
    ];

    $validator = Validator::make($request->all(), $rules, $message);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $user = Auth::guard('web')->user();

    $review = PropertyReview::where('user_id', $user->id)->where('property_id', $id)
      ->first();

    /**
     * if, property review of auth user does not exist then create a new one.
     * otherwise, update the existing review of that auth user.
     */
    if ($review == null) {
      PropertyReview::create($request->except('user_id', 'property_id') + [
        'user_id' => $user->id,
        'property_id' => $id
      ]);

      // now, store the average rating of this property
      $property = property::where('id', $id)->first();

      $property->update(['avg_rating' => $request->rating]);
    } else {
      $review->update($request->all());

      // now, get the average rating of this property
      $PropertyReviews = PropertyReview::where('property_id', $id)->get();

      $totalRating = 0;

      foreach ($PropertyReviews as $PropertyReview) {
        $totalRating += $PropertyReview->rating;
      }

      $avgRating = $totalRating / $PropertyReviews->count();

      // finally, store the average rating of this property
      $property = property::where('id', $id)->first();

      $property->update(['avg_rating' => $avgRating]);
    }

    if ($property->vendor_id != NULL) {
      $room_review_avg = Room::where('vendor_id', $property->vendor_id)->avg('avg_rating');
      $property_review_avg = property::where('vendor_id', $property->vendor_id)->avg('avg_rating');

      $avg = ($room_review_avg + $property_review_avg) / 2;

      $vendor = Vendor::where('id', $property->vendor_id)->first();
      $vendor->avg_rating = $avg;
      $vendor->save();
    }

    session()->flash('success', 'Review saved successfully!');

    return redirect()->back();
  }
}
