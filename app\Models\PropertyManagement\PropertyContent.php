<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyContent extends Model
{
  use HasFactory;

  protected $table = 'property_contents'; // Keep existing table name for now

  protected $fillable = [
    'language_id',
    'property_category_id',
    'property_id',
    'title',
    'slug',
    'description',
    'amenities',
    'meta_keywords',
    'meta_description'
  ];

  public function propertyCategory()
  {
    return $this->belongsTo('App\Models\PropertyManagement\PropertyCategory', 'property_category_id');
  }

  public function property()
  {
    return $this->belongsTo('App\Models\PropertyManagement\Property', 'property_id');
  }

  public function propertyContentLang()
  {
    return $this->belongsTo('App\Models\Language', 'language_id');
  }

  // Legacy method names for backward compatibility during transition
  public function packageCategory()
  {
    return $this->propertyCategory();
  }

  public function package()
  {
    return $this->property();
  }

  public function packageContentLang()
  {
    return $this->propertyContentLang();
  }
}
