<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyReview extends Model
{
  use HasFactory;

  protected $table = 'package_reviews'; // Keep existing table name for now

  protected $fillable = ['user_id', 'vendor_id', 'property_id', 'rating', 'comment'];

  public function propertyReviewedByUser()
  {
    return $this->belongsTo('App\Models\User', 'user_id', 'id');
  }

  public function reviewOfProperty()
  {
    return $this->belongsTo('App\Models\PropertyManagement\Property', 'property_id');
  }

  // Legacy method names for backward compatibility during transition
  public function packageReviewedByUser()
  {
    return $this->propertyReviewedByUser();
  }

  public function reviewOfPackage()
  {
    return $this->reviewOfProperty();
  }
}
