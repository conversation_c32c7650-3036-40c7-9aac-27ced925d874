<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyContent extends Model
{
  use HasFactory;

  protected $fillable = [
    'language_id',
    'property_category_id',
    'property_id',
    'title',
    'slug',
    'description',
    'amenities',
    'meta_keywords',
    'meta_description'
  ];

  public function PropertyCategory()
  {
    return $this->belongsTo('App\Models\PropertyManagement\PropertyCategory');
  }

  public function property()
  {
    return $this->belongsTo('App\Models\PropertyManagement\property');
  }

  public function PropertyContentLang()
  {
    return $this->belongsTo('App\Models\Language');
  }
}
