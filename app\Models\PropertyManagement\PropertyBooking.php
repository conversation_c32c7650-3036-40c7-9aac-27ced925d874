<?php

namespace App\Models\PropertyManagement;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyBooking extends Model
{
  use HasFactory;

  protected $table = 'property_bookings'; // Keep existing table name for now

  protected $fillable = [
    'booking_number',
    'user_id',
    'vendor_id',
    'customer_name',
    'customer_email',
    'customer_phone',
    'property_id',
    'visitors',
    'subtotal',
    'discount',
    'grand_total',
    'currency_symbol',
    'currency_symbol_position',
    'currency_text',
    'currency_text_position',
    'payment_method',
    'gateway_type',
    'attachment',
    'invoice',
    'payment_status',
    'comission',
    'received_amount',
    'commission_percentage',
    'conversation_id'
  ];

  public function property()
  {
    return $this->belongsTo('App\Models\PropertyManagement\Property', 'property_id', 'id');
  }

  public function propertyBookedByUser()
  {
    return $this->belongsTo('App\Models\User', 'user_id', 'id');
  }

  //vendor
  public function vendor()
  {
    return $this->belongsTo(Vendor::class, 'vendor_id', 'id');
  }
  //user
  public function user()
  {
    return $this->belongsTo(User::class, 'user_id', 'id');
  }

  // Legacy method names for backward compatibility during transition
  public function tourPackage()
  {
    return $this->property();
  }

  public function packageBookedByUser()
  {
    return $this->propertyBookedByUser();
  }
}
