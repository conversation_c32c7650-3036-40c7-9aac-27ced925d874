<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyLocation extends Model
{
  use HasFactory;

  protected $fillable = [
    'language_id',
    'property_id',
    'name',
    'latitude',
    'longitude'
  ];

  public function locationLang()
  {
    return $this->belongsTo('App\Models\Language');
  }

  public function propertyInfo()
  {
    return $this->belongsTo('App\Models\PropertyManagement\property', 'property_id', 'id');
  }
}
