<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| User Interface Routes
|--------------------------------------------------------------------------
*/


Route::prefix('vendor')->middleware('language')->group(function () {
  Route::get('/dashboard', 'Vendor\VendorController@index')->name('vendor.index');
  Route::get('/signup', 'Vendor\VendorController@signup')->name('vendor.signup');
  Route::post('/signup/submit', 'Vendor\VendorController@create')->name('vendor.signup_submit');
  Route::get('/login', 'Vendor\VendorController@login')->name('vendor.login');
  Route::post('/login/submit', 'Vendor\VendorController@authentication')->name('vendor.login_submit');

  Route::get('/email/verify', 'Vendor\VendorController@confirm_email');

  Route::get('/forget-password', 'Vendor\VendorController@forget_passord')->name('vendor.forget.password');
  Route::post('/send-forget-mail', 'Vendor\VendorController@forget_mail')->name('vendor.forget.mail');
  Route::get('/reset-password', 'Vendor\VendorController@reset_password')->name('vendor.reset.password');
  Route::post('/update-forget-password', 'Vendor\VendorController@update_password')->name('vendor.update-forget-password');
});


Route::prefix('vendor')->middleware('auth:vendor', 'EmailStatus:vendor', 'Deactive:vendor',)->group(function () {
  Route::get('dashboard', 'Vendor\VendorController@dashboard')->name('vendor.dashboard');
  Route::get('monthly-income', 'Vendor\VendorController@monthly_income')->name('vendor.monthly_income');
  Route::get('/change-password', 'Vendor\VendorController@change_password')->name('vendor.change_password');
  Route::post('/update-password', 'Vendor\VendorController@updated_password')->name('vendor.update_password');
  Route::get('/edit-profile', 'Vendor\VendorController@edit_profile')->name('vendor.edit.profile');
  Route::post('/profile/update', 'Vendor\VendorController@update_profile')->name('vendor.update_profile');
  Route::get('/logout', 'Vendor\VendorController@logout')->name('vendor.logout');


    // SMS Route

    Route::get('/sendSms', 'Vendor\SmsController@showSmsForm')->name('vendor.sendSms'); // Form display route
    Route::post('/sendSms', 'Vendor\SmsController@sendSms')->name('vendor.sendSmsPost'); // Form submission route


  // change vendor-panel theme (dark/light) route
  Route::post('/change-theme', 'Vendor\VendorController@changeTheme')->name('vendor.change_theme');


  // rooms management route start
  Route::prefix('rooms_management')->group(function () {
    Route::get('/rooms', 'Vendor\RoomController@rooms')->name('vendor.rooms_management.rooms');
    Route::get('/create_room', 'Vendor\RoomController@createRoom')->name('vendor.rooms_management.create_room');
    Route::post('/store_room', 'Vendor\RoomController@storeRoom')->name('vendor.rooms_management.store_room');


    //sliders images
    Route::post('/rooms_management/images-store', 'Vendor\RoomController@gallerystore')->name('vendor.rooms_management.imagesstore');
    Route::post('rooms_management/room-imagermv', 'Vendor\RoomController@imagermv')->name('vendor.rooms_management.imagermv');

    Route::post('rooms_management/room-img-dbrmv', 'Vendor\RoomController@imagedbrmv')->name('vendor.rooms_management.imgdbrmv');
    Route::get('rooms_management/room-images/{id}', 'Vendor\RoomController@images')->name('vendor.rooms_management.images');
    //sliders images end

    Route::get('/edit_room/{id}', 'Vendor\RoomController@editRoom')->name('vendor.rooms_management.edit_room');
    Route::get('/slider_images/{id}', 'Vendor\RoomController@getSliderImages');
    Route::post('/update_room/{id}', 'Vendor\RoomController@updateRoom')->name('vendor.rooms_management.update_room');
    Route::post('/delete_room', 'Vendor\RoomController@deleteRoom')->name('vendor.rooms_management.delete_room');
    Route::post('/bulk_delete_room', 'Vendor\RoomController@bulkDeleteRoom')->name('vendor.rooms_management.bulk_delete_room');
  });
  // rooms management route end

  // Room Bookings Routes
  Route::prefix('room_bookings')->group(function () {
    Route::get('/all_bookings', 'Vendor\RoomBookingController@bookings')->name('vendor.room_bookings.all_bookings');

    Route::get('/paid_bookings', 'Vendor\RoomBookingController@bookings')->name('vendor.room_bookings.paid_bookings');

    Route::get('/unpaid_bookings', 'Vendor\RoomBookingController@bookings')->name('vendor.room_bookings.unpaid_bookings');

    Route::post('/update_payment_status', 'Vendor\RoomBookingController@updatePaymentStatus')->name('vendor.room_bookings.update_payment_status');

    Route::get('/booking_details_and_edit/{id}', 'Vendor\RoomBookingController@editBookingDetails')->name('vendor.room_bookings.booking_details_and_edit');

    Route::post('/update_booking', 'Vendor\RoomBookingController@updateBooking')->name('vendor.room_bookings.update_booking');

    Route::post('/send_mail', 'Vendor\RoomBookingController@sendMail')->name('vendor.room_bookings.send_mail');

    Route::post('/delete_booking/{id}', 'Vendor\RoomBookingController@deleteBooking')->name('vendor.room_bookings.delete_booking');

    Route::post('/bulk_delete_booking', 'Vendor\RoomBookingController@bulkDeleteBooking')->name('vendor.room_bookings.bulk_delete_booking');

    Route::get('/get_booked_dates', 'Vendor\RoomBookingController@bookedDates')->name('vendor.room_bookings.get_booked_dates');

    Route::get('/booking_form', 'Vendor\RoomBookingController@bookingForm')->name('vendor.room_bookings.booking_form');

    Route::post('/make_booking', 'Vendor\RoomBookingController@makeBooking')->name('vendor.room_bookings.make_booking');
  });


  // properties management route start
  Route::prefix('property-management')->group(function () {

    Route::get('/properties', 'Vendor\PropertyController@properties')->name('vendor.properties_management.properties');

    Route::get('/create_property', 'Vendor\PropertyController@createProperty')->name('vendor.properties_management.create_property');

    Route::post('/store_property', 'Vendor\PropertyController@storeProperty')->name('vendor.properties_management.store_property');

    //sliders images
    Route::post('/images-store', 'Vendor\PropertyController@gallerystore')->name('vendor.properties_management.imagesstore');
    Route::post('property-imagermv', 'Vendor\PropertyController@imagermv')->name('vendor.properties_management.imagermv');

    Route::post('property-img-dbrmv', 'Vendor\PropertyController@imagedbrmv')->name('vendor.properties_management.imgdbrmv');
    Route::get('property-images/{id}', 'Vendor\PropertyController@images')->name('vendor.properties_management.images');
    //sliders images end

    Route::get('/edit_property/{id}', 'Vendor\PropertyController@editProperty')->name('vendor.properties_management.edit_property');

    Route::get('/slider_images/{id}', 'Vendor\PropertyController@getSliderImages');

    Route::post('/update_property/{id}', 'Vendor\PropertyController@updateProperty')->name('vendor.properties_management.update_property');

    Route::post('/delete_property', 'Vendor\PropertyController@deleteProperty')->name('vendor.properties_management.delete_property');

    Route::post('/bulk_delete_property', 'Vendor\PropertyController@bulkDeleteProperty')->name('vendor.properties_management.bulk_delete_property');

    Route::post('/store_location', 'Vendor\PropertyController@storeLocation')->name('vendor.properties_management.store_location');

    Route::get('/view_locations/{property_id}', 'Vendor\PropertyController@viewLocations')->name('vendor.properties_management.view_locations');

    Route::post('/update_location', 'Vendor\PropertyController@updateLocation')->name('vendor.properties_management.update_location');

    Route::post('/delete_location', 'Vendor\PropertyController@deleteLocation')->name('vendor.properties_management.delete_location');

    Route::post('/bulk_delete_location', 'Vendor\PropertyController@bulkDeleteLocation')->name('vendor.properties_management.bulk_delete_location');

    Route::post('/store_daywise_plan', 'Vendor\PropertyController@storeDaywisePlan')->name('vendor.properties_management.store_daywise_plan');

    Route::post('/store_timewise_plan', 'Vendor\PropertyController@storeTimewisePlan')->name('vendor.properties_management.store_timewise_plan');

    Route::get('/view_plans/{property_id}', 'Vendor\PropertyController@viewPlans')->name('vendor.properties_management.view_plans');

    Route::post('/update_daywise_plan', 'Vendor\PropertyController@updateDaywisePlan')->name('vendor.properties_management.update_daywise_plan');

    Route::post('/update_timewise_plan', 'Vendor\PropertyController@updateTimewisePlan')->name('vendor.properties_management.update_timewise_plan');

    Route::post('/delete_plan', 'Vendor\PropertyController@deletePlan')->name('vendor.properties_management.delete_plan');

    Route::post('/bulk_delete_plan', 'Vendor\PropertyController@bulkDeletePlan')->name('vendor.properties_management.bulk_delete_plan');
  });
  // properties management route end

  // Property Bookings Routes
  Route::prefix('property_bookings')->group(function () {
    Route::get('/all_bookings', 'Vendor\PropertyBookingController@bookings')->name('vendor.property_bookings.all_bookings');

    Route::get('/paid_bookings', 'Vendor\PropertyBookingController@bookings')->name('vendor.property_bookings.paid_bookings');

    Route::get('/unpaid_bookings', 'Vendor\PropertyBookingController@bookings')->name('vendor.property_bookings.unpaid_bookings');

    Route::post('/update_payment_status', 'Vendor\PropertyBookingController@updatePaymentStatus')->name('vendor.property_bookings.update_payment_status');

    Route::get('/booking_details/{id}', 'Vendor\PropertyBookingController@bookingDetails')->name('vendor.property_bookings.booking_details');

    Route::post('/send_mail', 'Vendor\PropertyBookingController@sendMail')->name('vendor.property_bookings.send_mail');

    Route::post('/delete_booking/{id}', 'Vendor\PropertyBookingController@deleteBooking')->name('vendor.property_bookings.delete_booking');

    Route::post('/bulk_delete_booking', 'Vendor\PropertyBookingController@bulkDeleteBooking')->name('vendor.property_bookings.bulk_delete_booking');
  });


  Route::prefix('withdraw')->middleware('Deactive')->group(function () {
    Route::get('/', 'Vendor\VendorWithdrawController@index')->name('vendor.withdraw');
    Route::get('/create', 'Vendor\VendorWithdrawController@create')->name('vendor.withdraw.create');
    Route::get('/get-method/input/{id}', 'Vendor\VendorWithdrawController@get_inputs');

    Route::get('/balance-calculation/{method}/{amount}', 'Vendor\VendorWithdrawController@balance_calculation');

    Route::post('/send-request', 'Vendor\VendorWithdrawController@send_request')->name('vendor.withdraw.send-request');
    Route::post('/witdraw/bulk-delete', 'Vendor\VendorWithdrawController@bulkDelete')->name('vendor.witdraw.bulk_delete_withdraw');
    Route::post('/witdraw/delete', 'Vendor\VendorWithdrawController@Delete')->name('vendor.witdraw.delete_withdraw');
  });

  Route::get('/transcation', 'Vendor\VendorController@transcation')->name('vendor.transcation');




  #====support tickets ============
  Route::prefix('support/ticket')->group(function () {
    Route::get('create', 'Vendor\SupportTicketController@create')->name('vendor.support_ticket.create');
    Route::post('store', 'Vendor\SupportTicketController@store')->name('vendor.support_ticket.store');
    Route::get('', 'Vendor\SupportTicketController@index')->name('vendor.support_tickets');

    Route::get('message/{id}', 'Vendor\SupportTicketController@message')->name('vendor.support_tickets.message');

    Route::post('zip-upload', 'Vendor\SupportTicketController@zip_file_upload')->name('vendor.support_ticket.zip_file.upload');

    Route::post('reply/{id}', 'Vendor\SupportTicketController@ticketreply')->name('vendor.support_ticket.reply');

    Route::post('delete/{id}', 'Vendor\SupportTicketController@delete')->name('vendor.support_tickets.delete');
    Route::post('bulk/delete/', 'Vendor\SupportTicketController@bulk_delete')->name('vendor.support_tickets.bulk_delete');
  });
});
