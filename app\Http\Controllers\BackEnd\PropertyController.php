<?php

namespace App\Http\Controllers\BackEnd;

use App\Http\Controllers\Controller;
use App\Http\Helpers\UploadFile;
use App\Http\Requests\CouponRequest;
use App\Models\BasicSettings\MailTemplate;
use App\Models\Commission;
use App\Models\Earning;
use App\Models\Language;
use App\Models\PropertyManagement\Coupon;
use App\Models\PropertyManagement\Property;
use App\Models\PropertyManagement\PropertyBooking;
use App\Models\PropertyManagement\PropertyCategory;
use App\Models\PropertyManagement\PropertyContent;
use App\Models\PropertyManagement\PropertyImage;
use App\Models\PropertyManagement\PropertyLocation;
use App\Models\PropertyManagement\PropertyPlan;
use App\Models\PropertyManagement\PropertyAmenity;
use App\Models\Transaction;
use App\Models\Vendor;
use App\Traits\MiscellaneousTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Mews\Purifier\Facades\Purifier;
use PDF;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\PHPMailer;

class PropertyController extends Controller
{
    use MiscellaneousTrait;

    public function settings()
    {
        $data = DB::table('basic_settings')
            ->select('property_category_status', 'property_rating_status', 'property_guest_checkout_status')
            ->where('uniqid', 12345)
            ->first();

        return view('backend.properties.settings', ['data' => $data]);
    }

    public function updateSettings(Request $request)
    {
        $rules = [
            'property_category_status' => 'required',
            'property_rating_status' => 'required',
            'property_guest_checkout_status' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        DB::table('basic_settings')->updateOrInsert(
            ['uniqid' => 12345],
            [
                'property_category_status' => $request->property_category_status,
                'property_rating_status' => $request->property_rating_status,
                'property_guest_checkout_status' => $request->property_guest_checkout_status
            ]
        );

        $request->session()->flash('success', 'Settings updated successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function coupons(Request $request)
    {
        $searchKey = null;

        if ($request->filled('search')) {
            $searchKey = $request['search'];
        }

        $coupons = Coupon::when($searchKey, function ($query, $searchKey) {
            return $query->where('name', 'like', '%' . $searchKey . '%')
                ->orWhere('code', 'like', '%' . $searchKey . '%');
        })
            ->orderByDesc('id')
            ->paginate(10);

        return view('backend.properties.coupons', compact('coupons'));
    }

    public function storeCoupon(CouponRequest $request)
    {
        $properties = $request->properties;

        if (empty($properties)) {
            $properties = [];
        }

        Coupon::create($request->except('properties') + [
            'properties' => json_encode($properties)
        ]);

        $request->session()->flash('success', 'New coupon added successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function updateCoupon(CouponRequest $request)
    {
        $properties = $request->properties;

        if (empty($properties)) {
            $properties = [];
        }

        $coupon = Coupon::find($request->id);

        $coupon->update($request->except('properties') + [
            'properties' => json_encode($properties)
        ]);

        $request->session()->flash('success', 'Coupon updated successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function destroyCoupon($id)
    {
        $coupon = Coupon::find($id);

        $coupon->delete();

        return redirect()->back()->with('success', 'Coupon deleted successfully!');
    }

    public function categories(Request $request)
    {
        $language = Language::where('code', $request->language)->firstOrFail();
        $information['language'] = $language;

        $information['categories'] = $language->propertyCategory()->orderByDesc('id')->get();

        return view('backend.properties.categories', $information);
    }

    public function storeCategory(Request $request)
    {
        $rules = [
            'language_id' => 'required',
            'name' => 'required|max:255',
            'status' => 'required|numeric',
            'serial_number' => 'required|numeric'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        PropertyCategory::create($request->all());

        $request->session()->flash('success', 'New category added successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function updateCategory(Request $request)
    {
        $rules = [
            'name' => 'required|max:255',
            'status' => 'required|numeric',
            'serial_number' => 'required|numeric'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $category = PropertyCategory::find($request->id);

        $category->update($request->all());

        $request->session()->flash('success', 'Category updated successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function deleteCategory(Request $request)
    {
        $category = PropertyCategory::find($request->id);

        $propertyContents = $category->propertyContentList;

        foreach ($propertyContents as $propertyContent) {
            $propertyContent->delete();
        }

        $category->delete();

        $request->session()->flash('success', 'Category deleted successfully!');

        return redirect()->back();
    }

    public function bulkDeleteCategory(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            $category = PropertyCategory::find($id);

            $propertyContents = $category->propertyContentList;

            foreach ($propertyContents as $propertyContent) {
                $propertyContent->delete();
            }

            $category->delete();
        }

        $request->session()->flash('success', 'Categories deleted successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function properties(Request $request)
    {
        $language = Language::where('is_default', 1)->firstOrFail();
        $information['language'] = $language;

        $languageId = $language->id;

        $vendor = $title = null;
        if ($request->filled('vendor')) {
            $vendor = $request['vendor'];
        }
        if ($request->filled('title')) {
            $title = $request['title'];
        }

        $propertyIds = [];
        if ($request->filled('title')) {
            $property_contents = PropertyContent::where('title', 'like', '%' . $title . '%')->get();
            foreach ($property_contents as $property_content) {
                if (!in_array($property_content->property_id, $propertyIds)) {
                    array_push($propertyIds, $property_content->property_id);
                }
            }
        }

        $information['properties'] = Property::with([
            'property_content' => function ($q) use ($languageId) {
                return $q->where('language_id', $languageId);
            }
        ])
            ->when($vendor, function ($query, $vendor) {
                if ($vendor == 'admin') {
                    return $query->where('properties.vendor_id', '=', null);
                } else {
                    return $query->where('properties.vendor_id', $vendor);
                }
            })
            ->when($title, function ($query) use ($propertyIds) {
                return $query->whereIn('id', $propertyIds);
            })
            ->orderBy('id', 'desc')
            ->paginate(10);

        $information['vendors'] = Vendor::get();
        $information['currencyInfo'] = MiscellaneousTrait::getCurrencyInfo();

        return view('backend.properties.properties', $information);
    }

    public function createProperty()
    {
        // get all the languages from db
        $information['languages'] = Language::all();

        $information['basicSettings'] = DB::table('basic_settings')
            ->select('property_category_status')
            ->where('uniqid', 12345)
            ->first();
        $information['vendors'] = Vendor::where('status', 1)->get();

        return view('backend.properties.create_property', $information);
    }

    public function gallerystore(Request $request)
    {
        $img = $request->file('file');
        $allowedExts = array('jpg', 'png', 'jpeg');
        $rules = [
            'file' => [
                function ($attribute, $value, $fail) use ($img, $allowedExts) {
                    $ext = $img->getClientOriginalExtension();
                    if (!in_array($ext, $allowedExts)) {
                        return $fail("Only png, jpg, jpeg images are allowed");
                    }
                }
            ]
        ];
        $messages = [
            'file.dimensions' => 'The file has invalid image dimensions ' . $img->getClientOriginalName()
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            $validator->getMessageBag()->add('error', 'true');
            return response()->json($validator->errors());
        }

        $filename = uniqid() . '.' . $img->getClientOriginalExtension();

        $img->move(public_path('assets/img/property-gallery/'), $filename);

        return response()->json(['status' => 'success', 'file_id' => $filename]);
    }

    public function imagermv(Request $request)
    {
        @unlink(public_path('assets/img/property-gallery/') . $request->fileid);
        return $request->fileid;
    }

    public function images($id)
    {
        $images = PropertyImage::where('property_id', $id)->get();
        return view('backend.properties.images', compact('images', 'id'));
    }

    public function imagedbrmv(Request $request)
    {
        $pi = PropertyImage::where('id', $request->fileid)->first();
        $property_id = $pi->property_id;
        $image_count = PropertyImage::where('property_id', $property_id)->get()->count();
        if ($image_count > 1) {
            @unlink(public_path('assets/img/property-gallery/') . $pi->image);
            $pi->delete();
            return $pi->id;
        } else {
            return 'false';
        }
    }

    // Amenities methods
    public function amenities(Request $request)
    {
        $language = Language::where('code', $request->language)->firstOrFail();
        $information['language'] = $language;

        $information['amenities'] = $language->propertyAmenity()->orderByDesc('id')->get();

        return view('backend.properties.amenities', $information);
    }

    public function storeAmenity(Request $request)
    {
        $rules = [
            'language_id' => 'required',
            'name' => 'required|max:255',
            'serial_number' => 'required|numeric'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        PropertyAmenity::create($request->all());

        $request->session()->flash('success', 'New amenity added successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function updateAmenity(Request $request)
    {
        $rules = [
            'name' => 'required|max:255',
            'serial_number' => 'required|numeric'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $amenity = PropertyAmenity::find($request->id);
        $amenity->update($request->all());

        $request->session()->flash('success', 'Amenity updated successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function deleteAmenity(Request $request)
    {
        $amenity = PropertyAmenity::find($request->id);
        $amenity->delete();

        $request->session()->flash('success', 'Amenity deleted successfully!');

        return redirect()->back();
    }

    public function bulkDeleteAmenity(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            $amenity = PropertyAmenity::find($id);
            $amenity->delete();
        }

        $request->session()->flash('success', 'Amenities deleted successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    // Legacy method names for backward compatibility
    public function properties(Request $request)
    {
        return $this->properties($request);
    }

    public function createProperty()
    {
        return $this->createProperty();
    }
}
