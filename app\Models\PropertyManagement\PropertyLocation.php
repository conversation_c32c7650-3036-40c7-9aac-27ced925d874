<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyLocation extends Model
{
  use HasFactory;

  protected $table = 'property_locations'; // Keep existing table name for now

  protected $fillable = [
    'language_id',
    'property_id',
    'name',
    'latitude',
    'longitude'
  ];

  public function locationLang()
  {
    return $this->belongsTo('App\Models\Language', 'language_id');
  }

  public function propertyInfo()
  {
    return $this->belongsTo('App\Models\PropertyManagement\Property', 'property_id', 'id');
  }

  // Legacy method names for backward compatibility during transition
  public function packageInfo()
  {
    return $this->propertyInfo();
  }
}
