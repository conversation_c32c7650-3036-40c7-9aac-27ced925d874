<?php

namespace App\Models\PropertyManagement;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Property extends Model
{
  use HasFactory;

  protected $table = 'properties'; // Keep existing table name for now

  protected $fillable = [
    'slider_imgs',
    'vendor_id',
    'featured_img',
    'plan_type',
    'max_persons',
    'number_of_days',
    'pricing_type',
    'property_price',
    'email',
    'phone',
    'avg_rating',
    'is_featured'
  ];

  public function propertyContent()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyContent');
  }

  //property_content
  public function property_content()
  {
    return $this->hasOne(PropertyContent::class, 'property_id', 'id');
  }

  public function propertyLocationList()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyLocation');
  }

  public function propertyPlanList()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyPlan');
  }

  public function images()
  {
    return $this->hasMany(PropertyImage::class, 'property_id', 'id');
  }

  public function propertyBooking()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyBooking');
  }

  public function propertyReview()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyReview');
  }

  //vendor
  public function vendor()
  {
    return $this->belongsTo(Vendor::class);
  }

  // Legacy method names for backward compatibility during transition
  public function packageContent()
  {
    return $this->propertyContent();
  }

  public function package_content()
  {
    return $this->property_content();
  }

  public function packageLocationList()
  {
    return $this->propertyLocationList();
  }

  public function packagePlanList()
  {
    return $this->propertyPlanList();
  }

  public function tourPackageBooking()
  {
    return $this->propertyBooking();
  }

  public function packageReview()
  {
    return $this->propertyReview();
  }
}
