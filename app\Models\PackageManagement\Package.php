<?php

namespace App\Models\PropertyManagement;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class property extends Model
{
  use HasFactory;

  protected $fillable = [
    'slider_imgs',
    'vendor_id',
    'featured_img',
    'plan_type',
    'max_persons',
    'number_of_days',
    'pricing_type',
    'property_price',
    'email',
    'phone',
    'avg_rating',
    'is_featured'
  ];

  public function PropertyContent()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyContent');
  }

  //property_content
  public function property_content()
  {
    return $this->hasOne(PropertyContent::class, 'property_id', 'id');
  }

  public function PropertyLocationList()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyLocation');
  }

  public function PropertyPlanList()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyPlan');
  }

  public function images()
  {
    return $this->hasMany(PropertyImage::class);
  }

  public function tourPropertyBooking()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyBooking');
  }

  public function PropertyReview()
  {
    return $this->hasMany('App\Models\PropertyManagement\PropertyReview');
  }

  //vendor
  public function vendor()
  {
    return $this->belongsTo(Vendor::class);
  }
}
