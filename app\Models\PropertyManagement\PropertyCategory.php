<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyCategory extends Model
{
    use HasFactory;

    protected $table = 'property_categories'; // Keep existing table name for now

    protected $fillable = [
        'language_id',
        'name',
        'status',
        'serial_number',
    ];

    public function propertyCategoryLang()
    {
        return $this->belongsTo('App\Models\Language', 'language_id');
    }

    public function propertyContentList()
    {
        return $this->hasMany('App\Models\PropertyManagement\PropertyContent', 'property_category_id');
    }

    // Legacy method names for backward compatibility during transition
    public function packageCategoryLang()
    {
        return $this->propertyCategoryLang();
    }

    public function packageContentList()
    {
        return $this->propertyContentList();
    }
}
