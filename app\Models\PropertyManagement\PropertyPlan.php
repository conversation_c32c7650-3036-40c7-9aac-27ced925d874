<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyPlan extends Model
{
  use HasFactory;

  protected $table = 'package_plans'; // Keep existing table name for now

  protected $fillable = [
    'language_id',
    'property_id',
    'day_number',
    'start_time',
    'end_time',
    'title',
    'plan'
  ];

  public function planLang()
  {
    return $this->belongsTo('App\Models\Language', 'language_id');
  }

  public function ownedByProperty()
  {
    return $this->belongsTo('App\Models\PropertyManagement\Property', 'property_id');
  }

  // Legacy method names for backward compatibility during transition
  public function ownedByPackage()
  {
    return $this->ownedByProperty();
  }
}
