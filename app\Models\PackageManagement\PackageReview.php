<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyReview extends Model
{
  use HasFactory;

  protected $fillable = ['user_id', 'vendor_id', 'property_id', 'rating', 'comment'];

  public function PropertyReviewedByUser()
  {
    return $this->belongsTo('App\Models\User', 'user_id', 'id');
  }

  public function reviewOfPackage()
  {
    return $this->belongsTo('App\Models\PropertyManagement\property');
  }
}
