<?php

namespace App\Http\Controllers\FrontEnd\Property;

use App\Http\Controllers\Controller;
use App\Models\Commission;
use App\Models\Earning;
use App\Models\PropertyManagement\Property;
use App\Models\PropertyManagement\PropertyBooking;
use App\Models\PaymentGateway\OnlineGateway;
use App\Models\Transaction as BookingTransaction;
use App\Models\Vendor;
use App\Traits\MiscellaneousTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Redirect;
use PayPal\Api\Amount;
use PayPal\Api\Item;
use PayPal\Api\ItemList;
use PayPal\Api\Payer;
use PayPal\Api\Payment;
use PayPal\Api\PaymentExecution;
use PayPal\Api\RedirectUrls;
use PayPal\Api\Transaction;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;

class PayPalController extends Controller
{
  use MiscellaneousTrait;

  private $api_context;

  public function __construct()
  {
    $data = OnlineGateway::whereKeyword('paypal')->first();
    $paypalData = json_decode($data->information, true);

    $paypal_conf = Config::get('paypal');
    $paypal_conf['client_id'] = $paypalData['client_id'];
    $paypal_conf['secret'] = $paypalData['client_secret'];
    $paypal_conf['settings']['mode'] = $paypalData['sandbox_status'] == 1 ? 'sandbox' : 'live';

    $this->api_context = new ApiContext(
      new OAuthTokenCredential(
        $paypal_conf['client_id'],
        $paypal_conf['secret']
      )
    );

    $this->api_context->setConfig($paypal_conf['settings']);
  }

  public function index(Request $request)
  {
    $propertyBookingInfo = $request->session()->get('propertyBookingInfo');
    $property = Property::find($request->session()->get('propertyId'));

    $currencyInfo = MiscellaneousTrait::getCurrencyInfo();

    // changing the currency before redirect to PayPal
    if ($currencyInfo->base_currency_text !== 'USD') {
      $rate = floatval($currencyInfo->base_currency_rate);
      $convertedTotal = round(($propertyBookingInfo['grand_total'] / $rate), 2);
    }

    $paypal_data = OnlineGateway::whereKeyword('paypal')->first();
    $information = json_decode($paypal_data->information, true);

    $payer = new Payer();
    $payer->setPaymentMethod('paypal');

    $item_1 = new Item();

    $language = MiscellaneousTrait::getLanguage();
    $propertyContent = $property->property_content()->where('language_id', $language->id)->first();
    $title = $propertyContent->title;

    $item_1->setName($title)
      ->setCurrency('USD')
      ->setQuantity(1)
      ->setPrice($currencyInfo->base_currency_text === 'USD' ? $propertyBookingInfo['grand_total'] : $convertedTotal);

    $item_list = new ItemList();
    $item_list->setItems(array($item_1));

    $amount = new Amount();
    $amount->setCurrency('USD')
      ->setTotal($currencyInfo->base_currency_text === 'USD' ? $propertyBookingInfo['grand_total'] : $convertedTotal);

    $transaction = new Transaction();
    $transaction->setAmount($amount)
      ->setItemList($item_list)
      ->setDescription($title . ' booking via PayPal');

    $redirect_urls = new RedirectUrls();
    $redirect_urls->setReturnUrl(route('property_booking.paypal.success'))
      ->setCancelUrl(route('property_booking.paypal.cancel'));

    $payment = new Payment();
    $payment->setIntent('Sale')
      ->setPayer($payer)
      ->setRedirectUrls($redirect_urls)
      ->setTransactions(array($transaction));

    try {
      $payment->create($this->api_context);
    } catch (\PayPal\Exception\PayPalConnectionException $ex) {
      return redirect()->back()->with('error', $ex->getMessage());
    }

    foreach ($payment->getLinks() as $link) {
      if ($link->getRel() == 'approval_url') {
        $redirect_url = $link->getHref();
        break;
      }
    }

    // put some data in session before redirect to paypal url
    $request->session()->put('payment_id', $payment->getId());

    if (isset($redirect_url)) {
      return Redirect::away($redirect_url);
    }

    return redirect()->back()->with('error', 'Unknown error occurred');
  }

  public function success(Request $request)
  {
    // get the data from session
    $propertyBookingInfo = $request->session()->get('propertyBookingInfo');
    $propertyId = $request->session()->get('propertyId');
    $paymentId = $request->session()->get('payment_id');

    $payment = Payment::get($paymentId, $this->api_context);
    $execution = new PaymentExecution();
    $execution->setPayerId($request->input('PayerID'));

    try {
      // execute the payment
      $result = $payment->execute($execution, $this->api_context);
    } catch (\PayPal\Exception\PayPalConnectionException $ex) {
      return redirect()->back()->with('error', $ex->getMessage());
    }

    if ($result->getState() == 'approved') {
      $propertyBookingInfo['gateway_type'] = 'online';
      $propertyBookingInfo['payment_status'] = 'completed';

      $propertyBooking = PropertyBooking::create($propertyBookingInfo);

      $this->saveTransaction($propertyBookingInfo, $propertyBooking->id);
      $this->saveEarning($propertyBooking);

      // remove all session data
      $request->session()->forget('propertyId');
      $request->session()->forget('propertyBookingInfo');
      $request->session()->forget('payment_id');

      return redirect()->route('property_booking.complete', ['id' => $propertyBooking->id]);
    }

    return redirect()->back()->with('error', 'Something went wrong');
  }

  public function cancel()
  {
    return redirect()->back()->with('error', 'Payment cancelled');
  }

  public function saveTransaction($propertyBookingInfo, $bookingId)
  {
    $transactionData = [];

    $transactionData['transaction_id'] = BookingTransaction::max('transaction_id') + 1;
    $transactionData['booking_id'] = $bookingId;
    $transactionData['transcation_type'] = 5;
    $transactionData['user_id'] = $propertyBookingInfo['user_id'];
    $transactionData['vendor_id'] = $propertyBookingInfo['vendor_id'];
    $transactionData['payment_status'] = $propertyBookingInfo['payment_status'];
    $transactionData['payment_method'] = $propertyBookingInfo['payment_method'];
    $transactionData['grand_total'] = $propertyBookingInfo['grand_total'];
    $transactionData['pre_balance'] = null;
    $transactionData['after_balance'] = null;
    $transactionData['gateway_type'] = $propertyBookingInfo['gateway_type'];
    $transactionData['currency_symbol'] = $propertyBookingInfo['currency_symbol'];
    $transactionData['currency_symbol_position'] = $propertyBookingInfo['currency_symbol_position'];

    BookingTransaction::create($transactionData);
  }

  public function saveEarning($propertyBooking)
  {
    $property = Property::find($propertyBooking->property_id);

    if (!is_null($property->vendor_id)) {
      $vendor = Vendor::find($property->vendor_id);

      $commission = Commission::first();
      $commissionPercentage = $commission->property_commission;

      $commissionAmount = ($propertyBooking->grand_total * $commissionPercentage) / 100;

      $vendorEarning = $propertyBooking->grand_total - $commissionAmount;

      Earning::updateOrCreate(
        ['vendor_id' => $vendor->id],
        ['total_earning' => \DB::raw("total_earning + $vendorEarning")]
      );

      $propertyBooking->update([
        'comission' => $commissionAmount,
        'received_amount' => $vendorEarning,
        'commission_percentage' => $commissionPercentage
      ]);
    }
  }
}
