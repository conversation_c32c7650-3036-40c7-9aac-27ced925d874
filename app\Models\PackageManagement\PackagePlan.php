<?php

namespace App\Models\PropertyManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyPlan extends Model
{
  use HasFactory;

  protected $fillable = [
    'language_id',
    'property_id',
    'day_number',
    'start_time',
    'end_time',
    'title',
    'plan'
  ];

  public function planLang()
  {
    return $this->belongsTo('App\Models\Language');
  }

  public function ownedByPackage()
  {
    return $this->belongsTo('App\Models\PropertyManagement\property');
  }
}
