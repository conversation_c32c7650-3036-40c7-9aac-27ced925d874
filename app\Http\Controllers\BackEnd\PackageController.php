<?php

namespace App\Http\Controllers\BackEnd;

use App\Http\Controllers\Controller;
use App\Http\Helpers\UploadFile;
use App\Http\Requests\CouponRequest;
use App\Models\BasicSettings\MailTemplate;
use App\Models\Commission;
use App\Models\Earning;
use App\Models\Language;
use App\Models\PropertyManagement\Coupon;
use App\Models\PropertyManagement\property;
use App\Models\PropertyManagement\PropertyBooking;
use App\Models\PropertyManagement\PropertyCategory;
use App\Models\PropertyManagement\PropertyContent;
use App\Models\PropertyManagement\PropertyImage;
use App\Models\PropertyManagement\PropertyLocation;
use App\Models\PropertyManagement\PropertyPlan;
use App\Models\PropertyManagement\PropertyAmenity;
use App\Models\Transaction;
use App\Models\Vendor;
use App\Traits\MiscellaneousTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Mews\Purifier\Facades\Purifier;
use PDF;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\PHPMailer;

class PropertyController extends Controller
{
    use MiscellaneousTrait;

    public function settings()
    {
        $data = DB::table('basic_settings')
            ->select('property_category_status', 'property_rating_status', 'property_guest_checkout_status')
            ->where('uniqid', 12345)
            ->first();

        return view('backend.properties.settings', ['data' => $data]);
    }

    public function updateSettings(Request $request)
    {
        $rules = [
            'property_category_status' => 'required',
            'property_rating_status' => 'required',
            'property_guest_checkout_status' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator->errors());
        }

        DB::table('basic_settings')->update([
            'property_category_status' => $request->property_category_status,
            'property_rating_status' => $request->property_rating_status,
            'property_guest_checkout_status' => $request->property_guest_checkout_status
        ]);

        session()->flash('success', 'Property settings updated successfully!');

        return redirect()->back();
    }


    public function coupons()
    {
        // get the coupons from db
        $information['coupons'] = Coupon::orderByDesc('id')->get();

        // also, get the currency information from db
        $information['currencyInfo'] = MiscellaneousTrait::getCurrencyInfo();

        $language = Language::where('is_default', 1)->first();

        $properties = property::all();

        $properties->map(function ($property) use ($language) {
            $property['title'] = $property->PropertyContent()->where('language_id', $language->id)->pluck('title')->first();
        });

        $information['properties'] = $properties;

        return view('backend.properties.coupons', $information);
    }

    public function storeCoupon(CouponRequest $request)
    {
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        if ($request->filled('properties')) {
            $properties = $request->properties;
        }

        Coupon::create($request->except('start_date', 'end_date', 'properties') + [
            'start_date' => date_format($startDate, 'Y-m-d'),
            'end_date' => date_format($endDate, 'Y-m-d'),
            'properties' => isset($properties) ? json_encode($properties) : null
        ]);

        session()->flash('success', 'New coupon added successfully!');

        return 'success';
    }

    public function updateCoupon(CouponRequest $request)
    {
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        if ($request->filled('properties')) {
            $properties = $request->properties;
        }

        Coupon::where('id', $request->id)->first()->update($request->except('start_date', 'end_date', 'properties') + [
            'start_date' => date_format($startDate, 'Y-m-d'),
            'end_date' => date_format($endDate, 'Y-m-d'),
            'properties' => isset($properties) ? json_encode($properties) : null
        ]);

        session()->flash('success', 'Coupon updated successfully!');

        return 'success';
    }

    public function destroyCoupon($id)
    {
        Coupon::where('id', $id)->first()->delete();

        return redirect()->back()->with('success', 'Coupon deleted successfully!');
    }


    public function categories(Request $request)
    {
        // first, get the language info from db
        $language = Language::where('code', $request->language)->firstOrFail();
        $information['language'] = $language;

        // then, get the property categories of that language from db
        $information['propertyCategories'] = PropertyCategory::where('language_id', $language->id)
            ->orderBy('id', 'desc')
            ->paginate(10);

        // also, get all the languages from db
        $information['langs'] = Language::all();

        return view('backend.properties.categories', $information);
    }

    public function storeCategory(Request $request)
    {
        $rules = [
            'language_id' => 'required',
            'name' => 'required',
            'status' => 'required',
            'serial_number' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }
        $data = [
            'name' => $request->name,
            'language_id' => $request->language_id,
            'serial_number' => $request->serial_number,
            'status' => $request->status,
        ];
        PropertyCategory::create($data);

        session()->flash('success', 'New property category added successfully!');

        return 'success';
    }

    public function updateCategory(Request $request)
    {
        $rules = [
            'name' => 'required',
            'status' => 'required',
            'serial_number' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $category = PropertyCategory::find($request->category_id);

          $category->update([
            'name' => $request->name,
            'slug' => createSlug($request->name),
            'serial_number' => $request->serial_number,
            'status' => $request->status,
          ]);

        session()->flash('success', 'Property category updated successfully!');

        return 'success';
    }

    public function deleteCategory(Request $request)
    {
        $PropertyCategory = PropertyCategory::where('id', $request->category_id)->first();

        if ($PropertyCategory->PropertyContentList()->count() > 0) {
            session()->flash('warning', 'First delete all the properties of this category!');

            return redirect()->back();
        }
        @unlink(public_path('assets/img/property-category/') . $PropertyCategory->image);
        $PropertyCategory->delete();

        session()->flash('success', 'Property category deleted successfully!');

        return redirect()->back();
    }

    public function bulkDeleteCategory(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            $PropertyCategory = PropertyCategory::where('id', $id)->first();

            if ($PropertyCategory->PropertyContentList()->count() > 0) {
                session()->flash('warning', 'First delete all the property of those category!');

                /**
                 * this 'success' is returning for ajax call.
                 * here, by returning the 'success' ajax will show the flash error message
                 */
                return 'success';
            }
            @unlink(public_path('assets/img/property-category/') . $PropertyCategory->image);
            $PropertyCategory->delete();
        }

        session()->flash('success', 'Property categories deleted successfully!');

        /**
         * this 'success' is returning for ajax call.
         * if return == 'success' then ajax will reload the page.
         */
        return 'success';
    }


    public function properties(Request $request)
    {
        $language = Language::where('is_default', 1)->firstOrFail();
        $information['language'] = $language;

        $languageId = $language->id;

        $vendor = $title = null;
        if ($request->filled('vendor')) {
            $vendor = $request['vendor'];
        }
        if ($request->filled('title')) {
            $title = $request['title'];
        }

        $propertyIds = [];
        if ($request->filled('title')) {
            $property_contents = PropertyContent::where('title', 'like', '%' . $title . '%')->get();
            foreach ($property_contents as $property_content) {
                if (!in_array($property_content->property_id, $propertyIds)) {
                    array_push($propertyIds, $property_content->property_id);
                }
            }
        }

        $information['properties'] = property::with([
            'property_content' => function ($q) use ($languageId) {
                return $q->where('language_id', $languageId);
            }
        ])
            ->when($vendor, function ($query, $vendor) {
                if ($vendor == 'admin') {
                    return $query->where('properties.vendor_id', '=', null);
                } else {
                    return $query->where('properties.vendor_id', $vendor);
                }
            })
            ->when($title, function ($query) use ($propertyIds) {
                return $query->whereIn('id', $propertyIds);
            })
            ->orderBy('id', 'desc')
            ->paginate(10);

        $information['vendors'] = Vendor::get();
        $information['currencyInfo'] = MiscellaneousTrait::getCurrencyInfo();

        return view('backend.properties.properties', $information);
    }

    public function createProperty()
    {
        // get all the languages from db
        $information['languages'] = Language::all();

        $information['basicSettings'] = DB::table('basic_settings')
            ->select('property_category_status')
            ->where('uniqid', 12345)
            ->first();
        $information['vendors'] = Vendor::where('status', 1)->get();

        return view('backend.properties.create_property', $information);
    }

    public function gallerystore(Request $request)
    {
        $img = $request->file('file');
        $allowedExts = array('jpg', 'png', 'jpeg');
        $rules = [
            'file' => [
                function ($attribute, $value, $fail) use ($img, $allowedExts) {
                    $ext = $img->getClientOriginalExtension();
                    if (!in_array($ext, $allowedExts)) {
                        return $fail("Only png, jpg, jpeg images are allowed");
                    }
                }
            ]
        ];
        $messages = [
            'file.dimensions' => 'The file has invalid image dimensions ' . $img->getClientOriginalName()
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            $validator->getMessageBag()->add('error', 'true');
            return response()->json($validator->errors());
        }
        $filename = uniqid() . '.jpg';
        @mkdir(public_path('assets/img/property-gallery/'), 0775, true);
        $img->move(public_path('assets/img/property-gallery/'), $filename);
        $pi = new PropertyImage();
        if (!empty($request->property_id)) {
            $pi->property_id = $request->property_id;
        }
        $pi->image = $filename;
        $pi->save();
        return response()->json(['status' => 'success', 'file_id' => $pi->id]);
    }

    public function images($portid)
    {
        $images = PropertyImage::where('property_id', $portid)->get();
        return $images;
    }

    public function imagedbrmv(Request $request)
    {
        $pi = PropertyImage::where('id', $request->fileid)->first();
        $property_id = $pi->property_id;
        $image_count = PropertyImage::where('property_id', $property_id)->get()->count();
        if ($image_count > 1) {
            @unlink(public_path('assets/img/property-gallery/') . $pi->image);
            $pi->delete();
            return $pi->id;
        } else {
            return 'false';
        }
    }

    public function storeProperty(Request $request)
    {
        $rules = [
            'slider_images' => 'required',
            'plan_type' => 'required',
            'pricing_type' => 'required',
            'fixed_property_price' => 'required_if:pricing_type,==,fixed',
            'per_person_property_price' => 'required_if:pricing_type,==,per-person'
        ];

        $featuredImgURL = $request->featured_img;

        $allowedExtensions = array('jpg', 'jpeg', 'png', 'svg');
        $featuredImgExt = $featuredImgURL->getClientOriginalExtension();


        $rules['featured_img'] = [
            'required',
            function ($attribute, $value, $fail) use ($allowedExtensions, $featuredImgExt) {
                if (!in_array($featuredImgExt, $allowedExtensions)) {
                    $fail('Only .jpg, .jpeg, .png and .svg file is allowed for featured image.');
                }
            }
        ];

        $messages = [
            'featured_img.required' => 'The property\'s featured image is required.',
        ];

        $languages = Language::all();

        $settings = DB::table('basic_settings')->select('property_category_status')
            ->where('uniqid', 12345)
            ->first();

        foreach ($languages as $language) {
            $rules[$language->code . '_title'] = 'required|max:255';

            if ($settings->property_category_status == 1) {
                $rules[$language->code . '_category'] = 'required';
            }

            $rules[$language->code . '_description'] = 'required|min:15';

            $messages[$language->code . '_title.required'] = 'The title field is required for ' . $language->name . ' language';

            $messages[$language->code . '_title.max'] = 'The title field cannot contain more than 255 characters for ' . $language->name . ' language';

            if ($settings->property_category_status == 1) {
                $messages[$language->code . '_category.required'] = 'The category field is required for ' . $language->name . ' language';
            }

            $messages[$language->code . '_description.required'] = 'The description field is required for ' . $language->name . ' language';

            $messages[$language->code . '_description.min'] = 'The description field atleast have 15 characters for ' . $language->name . ' language';
        }

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $property = new property();
        if ($request->hasFile('featured_img')) {
            $filename = time() . '.' . $featuredImgURL->getClientOriginalExtension();
            $directory = public_path('assets/img/property/');
            @mkdir($directory, 0775, true);
            $request->file('featured_img')->move($directory, $filename);
            $property->featured_img = $filename;
        }
        $property->vendor_id = $request->vendor_id;
        $property->plan_type = $request->plan_type;
        $property->number_of_days = $request->number_of_days;
        $property->max_persons = $request->max_persons;
        $property->pricing_type = $request->pricing_type;

        if ($request->pricing_type == 'fixed') {
            $property->property_price = $request->fixed_property_price;
        } elseif ($request->pricing_type == 'per-person') {
            $property->property_price = $request->per_person_property_price;
        }

        $property->email = $request->email;
        $property->phone = $request->phone;
        $property->save();

        $slders = $request->slider_images;

        foreach ($slders as $key => $id) {
            $property_image = PropertyImage::where('id', $id)->first();
            if ($property_image) {
                $property_image->property_id = $property->id;
                $property_image->save();
            }
        }

        foreach ($languages as $language) {
            $PropertyContent = new PropertyContent();
            $PropertyContent->language_id = $language->id;
            $PropertyContent->property_category_id = $request[$language->code . '_category'];
            $PropertyContent->property_id = $property->id;
            $PropertyContent->title = $request[$language->code . '_title'];
            $PropertyContent->slug = createSlug($request[$language->code . '_title']);
            $PropertyContent->description = Purifier::clean($request[$language->code . '_description'], 'youtube');

            // Handle amenities
            $amenities = $request[$language->code . '_amenities'];
            if (!empty($amenities)) {
                $PropertyContent->amenities = json_encode($amenities);
            }

            $PropertyContent->meta_keywords = $request[$language->code . '_meta_keywords'];
            $PropertyContent->meta_description = $request[$language->code . '_meta_description'];
            $PropertyContent->save();
        }

        session()->flash('success', 'New property added successfully!');

        return 'success';
    }

    public function updateFeaturedProperty(Request $request)
    {
        $property = property::where('id', $request->propertyId)->first();

        if ($request->is_featured == 1) {
            $property->update(['is_featured' => 1]);

            session()->flash('success', 'Property featured successfully!');
        } else {
            $property->update(['is_featured' => 0]);

            session()->flash('success', 'Property Unfeatured successfully!');
        }

        return redirect()->back();
    }

    public function editProperty($id)
    {
        $information['property'] = property::where('id', $id)->firstOrFail();

        // get all the languages from db
        $information['languages'] = Language::all();

        $information['basicSettings'] = DB::table('basic_settings')
            ->select('property_category_status')
            ->where('uniqid', 12345)
            ->first();

        $information['vendors'] = Vendor::where('status', 1)->get();

        return view('backend.properties.edit_property', $information);
    }

    public function updateProperty(Request $request, $id)
    {
        $rules = [
            'plan_type' => 'required',
            'pricing_type' => 'required',
            'fixed_property_price' => 'required_if:pricing_type,==,fixed',
            'per_person_property_price' => 'required_if:pricing_type,==,per-person'
        ];

        $featuredImgURL = $request->featured_img;
        $allowedExtensions = array('jpg', 'jpeg', 'png', 'svg');

        if ($request->hasFile('featured_img')) {
            $featuredImgExt = $featuredImgURL->getClientOriginalExtension();
            $rules['featured_img'] = function ($attribute, $value, $fail) use ($allowedExtensions, $featuredImgExt) {
                if (!in_array($featuredImgExt, $allowedExtensions)) {
                    $fail('Only .jpg, .jpeg, .png and .svg file is allowed for featured image.');
                }
            };
        }

        $languages = Language::all();
        $bs = DB::table('basic_settings')->select('property_category_status')->first();

        foreach ($languages as $language) {
            $rules[$language->code . '_title'] = 'required|max:255';

            if ($bs->property_category_status == 1) {
                $rules[$language->code . '_category'] = 'required';
            }

            $rules[$language->code . '_description'] = 'required|min:15';

            $messages[$language->code . '_title.required'] = 'The title field is required for ' . $language->name . ' language';

            $messages[$language->code . '_title.max'] = 'The title field cannot contain more than 255 characters for ' . $language->name . ' language';

            if ($bs->property_category_status == 1) {
                $messages[$language->code . '_category.required'] = 'The category field is required for ' . $language->name . ' language';
            }

            $messages[$language->code . '_description.required'] = 'The description field is required for ' . $language->name . ' language';

            $messages[$language->code . '_description.min'] = 'The description field atleast have 15 characters for ' . $language->name . ' language';
        }

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $property = property::where('id', $id)->first();

        if ($request->hasFile('featured_img')) {
            $filename = time() . '.' . $featuredImgURL->getClientOriginalExtension();
            $directory = public_path('assets/img/property/');
            @mkdir($directory, 0775, true);
            @unlink($directory . $property->featured_img);
            $request->file('featured_img')->move($directory, $filename);
            $featured_image = $filename;
        }

        // get the property price that admin has selected
        if ($request->pricing_type == 'negotiable') {
            $amount = null;
        } elseif ($request->pricing_type == 'fixed') {
            $amount = $request->fixed_property_price;
        } elseif ($request->pricing_type == 'per-person') {
            $amount = $request->per_person_property_price;
        }

        $property->update([
            'number_of_days' => $request->number_of_days,
            'vendor_id' => $request->vendor_id,
            'featured_img' => $request->hasFile('featured_img') ? $featured_image : $property->featured_img,
            'plan_type' => $request->plan_type,
            'max_persons' => $request->max_persons,
            'pricing_type' => $request->pricing_type,
            'property_price' => isset($amount) ? $amount : $property->property_price,
            'email' => $request->email,
            'phone' => $request->phone
        ]);

        foreach ($languages as $language) {
            $PropertyContent = PropertyContent::where('property_id', $id)
                ->where('language_id', $language->id)
                ->first();

            // Handle amenities
            $amenities = $request[$language->code . '_amenities'];
            $amenitiesJson = !empty($amenities) ? json_encode($amenities) : null;

            $content = [
                'language_id' => $language->id,
                'property_id' => $property->id,
                'property_category_id' => $request[$language->code . '_category'],
                'title' => $request[$language->code . '_title'],
                'slug' => createSlug($request[$language->code . '_title']),
                'description' => Purifier::clean($request[$language->code . '_description'], 'youtube'),
                'amenities' => $amenitiesJson,
                'meta_keywords' => $request[$language->code . '_meta_keywords'],
                'meta_description' => $request[$language->code . '_meta_description']
            ];

            if (!empty($PropertyContent)) {
                $PropertyContent->update($content);
            } else {
                PropertyContent::create($content);
            }
        }

        session()->flash('success', 'Property updated successfully!');

        return 'success';
    }

    public function deleteProperty(Request $request)
    {
        $property = property::where('id', $request->property_id)->first();

        if ($property->PropertyLocationList()->count() > 0) {
            $locations = $property->PropertyLocationList()->get();
            foreach ($locations as $location) {
                $location->delete();
            }
        }

        if ($property->PropertyPlanList()->count() > 0) {
            $plans = $property->PropertyPlanList()->get();
            foreach ($plans as $plan) {
                $plan->delete();
            }
        }

        // first, delete all the contents of this property
        $contents = $property->PropertyContent()->get();

        foreach ($contents as $content) {
            $content->delete();
        }

        // second, delete all the slider images of this property
        $sliders = PropertyImage::where('property_id', $property->id)->get();
        foreach ($sliders as $slider) {
            @unlink(public_path('assets/img/property-gallery/') . $slider->image);
        }

        // third, delete featured image of this property
        @unlink(public_path('assets/img/property/') . $property->featured_img);

        // finally, delete this property
        $property->delete();

        session()->flash('success', 'Property deleted successfully!');

        return redirect()->back();
    }

    public function bulkdeleteProperty(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            $property = property::where('id', $id)->first();

            if ($property->PropertyLocationList()->count() > 0) {
                $locations = $property->PropertyLocationList()->get();
                foreach ($locations as $location) {
                    $location->delete();
                }
            }

            if ($property->PropertyPlanList()->count() > 0) {
                $plans = $property->PropertyPlanList()->get();
                foreach ($plans as $plan) {
                    $plan->delete();
                }
            }

            // first, delete all the contents of this property
            $contents = $property->PropertyContent()->get();

            foreach ($contents as $content) {
                $content->delete();
            }

            // second, delete all the slider images of this property
            $sliders = PropertyImage::where('property_id', $property->id)->get();
            foreach ($sliders as $slider) {
                @unlink(public_path('assets/img/property-gallery/') . $slider->image);
            }

            // third, delete featured image of this property
            @unlink(public_path('assets/img/property/') . $property->featured_img);

            // finally, delete this property
            $property->delete();
        }

        session()->flash('success', 'Property deleted successfully!');

        /**
         * this 'success' is returning for ajax call.
         * if return == 'success' then ajax will reload the page.
         */
        return 'success';
    }


    public function storeLocation(Request $request)
    {
        $rule = [
            'name' => 'required'
        ];

        $validator = Validator::make($request->all(), $rule);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $lang = Language::where('id', $request->language_id)->first();

        PropertyLocation::create($request->except('language_id') + [
            'language_id' => $lang->id
        ]);

        session()->flash('success', 'New location added successfully!');

        return 'success';
    }

    public function viewLocations(Request $request, $property_id)
    {
        // first, get the language info from db
        $information['langs'] = Language::all();
        $language = Language::where('code', $request->language)->firstOrFail();
        $information['language'] = $language;

        // then, get the locations of selected property
        $information['locations'] = PropertyLocation::where('language_id', $language->id)
            ->where('property_id', $property_id)
            ->orderBy('id', 'desc')
            ->get();

        return view('backend.properties.locations', $information);
    }

    public function updateLocation(Request $request)
    {
        $rule = [
            'name' => 'required'
        ];

        $validator = Validator::make($request->all(), $rule);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        PropertyLocation::where('id', $request->location_id)->first()->update($request->all());

        session()->flash('success', 'Location updated successfully!');

        return 'success';
    }

    public function deleteLocation(Request $request)
    {
        PropertyLocation::where('id', $request->location_id)->first()->delete();

        session()->flash('success', 'Location deleted successfully!');

        return redirect()->back();
    }

    public function bulkDeleteLocation(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            PropertyLocation::where('id', $id)->first()->delete();
        }

        session()->flash('success', 'Locations deleted successfully!');

        /**
         * this 'success' is returning for ajax call.
         * if return == 'success' then ajax will reload the page.
         */
        return 'success';
    }


    public function storeDaywisePlan(Request $request)
    {
        $rules = [
            'day_number' => 'required',
            'plan' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $lang = Language::where('id', $request->language_id)->first();

        PropertyPlan::create($request->except('language_id') + [
            'language_id' => $lang->id,
            'plan' => Purifier::clean($request->plan, 'youtube')
        ]);

        session()->flash('success', 'New plan added successfully!');

        return Response::json('success', 200);
    }

    public function storeTimewisePlan(Request $request)
    {
        $rules = [
            'start_time' => 'required',
            'end_time' => 'required',
            'plan' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $lang = Language::where('id', $request->language_id)->first();

        PropertyPlan::create($request->except('language_id') + [
            'language_id' => $lang->id,
            'plan' => Purifier::clean($request->plan, 'youtube')
        ]);

        session()->flash('success', 'New plan added successfully!');

        return Response::json('success', 200);
    }

    public function viewPlans(Request $request, $property_id)
    {
        $information['langs'] = Language::all();
        // first, get the language info from db
        $language = Language::where('code', $request->language)->firstOrFail();
        $information['language'] = $language;

        // then, get the plans of selected property
        $information['plans'] = PropertyPlan::where('language_id', $language->id)
            ->where('property_id', $property_id)
            ->orderBy('id', 'desc')
            ->paginate(10);

        $property = property::where('id', $property_id)->firstOrFail();
        $information['property'] = $property;

        if ($property->plan_type == 'daywise') {
            return view('backend.properties.daywise_plans', $information);
        } else if ($property->plan_type == 'timewise') {
            return view('backend.properties.timewise_plans', $information);
        }
    }

    public function updateDaywisePlan(Request $request)
    {
        $rules = [
            'day_number' => 'required',
            'edit_plan' => 'required'
        ];

        $messages = [];
        $messages['edit_plan.required'] = 'The plan feild is required.';

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }
        $in = $request->all();
        $in['plan'] = Purifier::clean($request->edit_plan, 'youtube');

        PropertyPlan::where('id', $request->plan_id)->first()->update($in);

        session()->flash('success', 'Plan updated successfully!');

        return 'success';
    }

    public function updateTimewisePlan(Request $request)
    {
        $rules = [
            'start_time' => 'required',
            'end_time' => 'required',
            'edit_plan' => 'required'
        ];
        $messages = [];
        $messages['edit_plan'] = 'The plan feild is required.';

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }
        $in = $request->all();
        $in['plan'] = Purifier::clean($request->edit_plan, 'youtube');

        PropertyPlan::where('id', $request->plan_id)->first()->update($in);

        session()->flash('success', 'Plan updated successfully!');

        return 'success';
    }

    public function deletePlan(Request $request)
    {
        PropertyPlan::where('id', $request->plan_id)->first()->delete();

        session()->flash('success', 'Plan deleted successfully!');

        return redirect()->back();
    }

    public function bulkDeletePlan(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            PropertyPlan::where('id', $id)->first()->delete();
        }

        session()->flash('success', 'Plans deleted successfully!');

        /**
         * this 'success' is returning for ajax call.
         * if return == 'success' then ajax will reload the page.
         */
        return 'success';
    }

    public function bookings(Request $request)
    {
        $booking_number = $title = null;

        if ($request->filled('booking_no')) {
            $booking_number = $request['booking_no'];
        }

        $propertyIds = [];
        if ($request->input('title')) {
            $title = $request->title;
            $property_contents = PropertyContent::where('title', 'like', '%' . $title . '%')->get();
            foreach ($property_contents as $property_content) {
                if (!in_array($property_content->property_id, $propertyIds)) {
                    array_push($propertyIds, $property_content->property_id);
                }
            }
        }

        if (URL::current() == Route::is('admin.property_bookings.all_bookings')) {
            $bookings = PropertyBooking::when($booking_number, function ($query, $booking_number) {
                return $query->where('booking_number', 'like', '%' . $booking_number . '%');
            })
                ->when($title, function ($query) use ($propertyIds) {
                    return $query->whereIn('property_id', $propertyIds);
                })
                ->orderBy('id', 'desc')
                ->paginate(10);
        } else if (URL::current() == Route::is('admin.property_bookings.paid_bookings')) {
            $bookings = PropertyBooking::when($booking_number, function ($query, $booking_number) {
                return $query->where('booking_number', 'like', '%' . $booking_number . '%');
            })
                ->when($title, function ($query) use ($propertyIds) {
                    return $query->whereIn('property_id', $propertyIds);
                })
                ->where('payment_status', 1)
                ->orderBy('id', 'desc')
                ->paginate(10);
        } else if (URL::current() == Route::is('admin.property_bookings.unpaid_bookings')) {
            $bookings = PropertyBooking::when($booking_number, function ($query, $booking_number) {
                return $query->where('booking_number', 'like', '%' . $booking_number . '%');
            })
                ->when($title, function ($query) use ($propertyIds) {
                    return $query->whereIn('property_id', $propertyIds);
                })
                ->where('payment_status', 0)
                ->orderBy('id', 'desc')
                ->paginate(10);
        }

        return view('backend.properties.bookings', compact('bookings'));
    }

    public function updatePaymentStatus(Request $request)
    {
        $PropertyBooking = PropertyBooking::where('id', $request->booking_id)->first();

        if ($request->payment_status == 1) {
            //calculate commission start
            if (!empty($PropertyBooking)) {
                if ($PropertyBooking->vendor_id != NULL) {
                    $vendor_id = $PropertyBooking->vendor_id;
                } else {
                    $vendor_id = NULL;
                }
            } else {
                $vendor_id = NULL;
            }

            //calculate commission
            $percent = Commission::select('property_booking_commission')->first();

            $commission = ($PropertyBooking->grand_total * $percent->property_booking_commission) / 100;

            //get vendor
            $vendor = Vendor::where('id', $PropertyBooking->vendor_id)->first();

            //add blance to admin revinue
            $earning = Earning::first();

            $earning->total_revenue = $earning->total_revenue + $PropertyBooking->grand_total;
            if ($vendor) {
                $earning->total_earning = $earning->total_earning + $commission;
            } else {
                $earning->total_earning = $earning->total_earning + $PropertyBooking->grand_total;
            }
            $earning->save();
            //store Balance  to vendor

            if ($vendor) {
                $pre_balance = $vendor->amount;
                $received_amount = $vendor->amount + ($PropertyBooking->grand_total - $commission);
                $vendor->amount = $received_amount;
                $vendor->save();
                $after_balance = $vendor->amount;

                $booking_received_amount = $PropertyBooking->grand_total - $commission;
            } else {
                $received_amount = NULL;
                $after_balance = NULL;
                $pre_balance = NULL;
                $booking_received_amount = NULL;
            }
            //calculate commission end

            $PropertyBooking->update([
                'payment_status' => 1,
                'comission' => $commission,
                'received_amount' => $booking_received_amount,
            ]);

            //store data to transcation table
            $data = [
                'transcation_id' => time(),
                'booking_id' => $PropertyBooking->id,
                'transcation_type' => 5,
                'user_id' => null,
                'vendor_id' => $vendor_id,
                'payment_status' => 1,
                'payment_method' => $PropertyBooking->payment_method,
                'grand_total' => $PropertyBooking->grand_total,
                'commission' => $PropertyBooking->comission,
                'pre_balance' => $pre_balance,
                'after_balance' => $after_balance,
                'gateway_type' => $PropertyBooking->gateway_type,
                'currency_symbol' => $PropertyBooking->currency_symbol,
                'currency_symbol_position' => $PropertyBooking->currency_symbol_position,
            ];
            store_transaction($data);
        } else {
            $PropertyBooking->update(['payment_status' => 0]);
            //calculate commission start
            if (!empty($PropertyBooking)) {
                if ($PropertyBooking->vendor_id != NULL) {
                    $vendor_id = $PropertyBooking->vendor_id;
                } else {
                    $vendor_id = NULL;
                }
            } else {
                $vendor_id = NULL;
            }
            //store data to transcation table
            $transcation = Transaction::where([['booking_id', $PropertyBooking->id], ['transcation_type', 5]])->first();
            $transcation->update(['payment_status' => 0]);

            //calculate commission
            $percent = Commission::select('property_booking_commission')->first();

            $commission = ($PropertyBooking->grand_total * $percent->property_booking_commission) / 100;

            //add blance to admin revinue
            $earning = Earning::first();
            $earning->total_revenue = $earning->total_revenue - $PropertyBooking->grand_total;
            $earning->total_earning = $earning->total_earning - $commission;
            $earning->save();
            //store Balance  to vendor
            $vendor = Vendor::where('id', $PropertyBooking->vendor_id)->first();
            if ($vendor) {
                $vendor->amount = $vendor->amount - ($PropertyBooking->grand_total - $commission);
                $vendor->save();
            }
        }

        // delete previous invoice from local storage
        if (
            !is_null($PropertyBooking->invoice) &&
            file_exists(public_path('assets/invoices/properties/') . $PropertyBooking->invoice)
        ) {
            @unlink(public_path('assets/invoices/properties/') . $PropertyBooking->invoice);
        }

        // then, generate an invoice in pdf format
        $invoice = $this->generateInvoice($PropertyBooking);

        // update the invoice field information in database
        $PropertyBooking->update(['invoice' => $invoice]);

        // finally, send a mail to the customer with the invoice
        $this->sendMailForPaymentStatus($PropertyBooking, $request->payment_status);

        session()->flash('success', 'Payment status updated successfully!');

        return redirect()->back();
    }

    public function bookingDetails($id)
    {
        $details = PropertyBooking::where('id', $id)->firstOrFail();

        $language = Language::where('is_default', 1)->firstOrFail();

        /**
         * to get the property title first get the property info using eloquent relationship
         * then, get the property content info of that property using eloquent relationship
         * after that, we can access the property title
         * also, get the property category using eloquent relationship
         */
        $propertyInfo = $details->tourProperty()->firstOrFail();

        $PropertyContentInfo = $propertyInfo->PropertyContent()->where('language_id', $language->id)
            ->firstOrFail();
        $propertyTitle = $PropertyContentInfo->title;

        $PropertyCategoryInfo = $PropertyContentInfo->PropertyCategory()->first();

        if (!is_null($PropertyCategoryInfo)) {
            $PropertyCategoryName = $PropertyCategoryInfo->name;
        } else {
            $PropertyCategoryName = null;
        }

        return view(
            'backend.properties.booking_details',
            compact('details', 'propertyTitle', 'PropertyCategoryName')
        );
    }

    public function sendMail(Request $request)
    {
        $rules = [
            'subject' => 'required',
            'message' => 'required',
        ];

        $messages = [
            'subject.required' => 'The email subject field is required.',
            'message.required' => 'The email message field is required.'
        ];

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        // get the mail's smtp information from db
        $mailInfo = DB::table('basic_settings')
            ->select('smtp_status', 'smtp_host', 'smtp_port', 'encryption', 'smtp_username', 'smtp_password', 'from_mail', 'from_name')
            ->first();

        // initialize a new mail
        $mail = new PHPMailer(true);

        // if smtp status == 1, then set some value for PHPMailer
        if ($mailInfo->smtp_status == 1) {
            $mail->isSMTP();
            $mail->Host       = $mailInfo->smtp_host;
            $mail->SMTPAuth   = true;
            $mail->Username   = $mailInfo->smtp_username;
            $mail->Password   = $mailInfo->smtp_password;

            if ($mailInfo->encryption == 'TLS') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }

            $mail->Port       = $mailInfo->smtp_port;
        }

        // finally add other informations and send the mail
        try {
            // Recipients
            $mail->setFrom($mailInfo->from_mail, $mailInfo->from_name);
            $mail->addAddress($request->customer_email);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $request->subject;
            $mail->Body    = Purifier::clean($request->message, 'youtube');

            $mail->send();

            session()->flash('success', 'Mail has been sent!');

            /**
             * this 'success' is returning for ajax call.
             * if return == 'success' then ajax will reload the page.
             */
            return 'success';
        } catch (Exception $e) {
            session()->flash('warning', 'Mail could not be sent!');

            /**
             * this 'success' is returning for ajax call.
             * if return == 'success' then ajax will reload the page.
             */
            return 'success';
        }
    }

    public function deleteBooking(Request $request, $id)
    {
        $PropertyBooking = PropertyBooking::where('id', $id)->first();

        // first, delete the attachment
        if (
            !is_null($PropertyBooking->attachment) &&
            file_exists(public_path('assets/img/attachments/properties/') . $PropertyBooking->attachment)
        ) {
            @unlink(public_path('assets/img/attachments/properties/') . $PropertyBooking->attachment);
        }

        // second, delete the invoice
        if (
            !is_null($PropertyBooking->invoice) &&
            file_exists(public_path('assets/invoices/properties/') . $PropertyBooking->invoice)
        ) {
            @unlink(public_path('assets/invoices/properties/') . $PropertyBooking->invoice);
        }

        // finally, delete the property booking record from db
        $PropertyBooking->delete();

        session()->flash('success', 'Property rental record deleted successfully!');

        return redirect()->back();
    }

    public function bulkDeleteBooking(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            $PropertyBooking = PropertyBooking::where('id', $id)->first();

            // first, delete the attachment
            if (
                !is_null($PropertyBooking->attachment) &&
                file_exists(public_path('assets/img/attachments/properties/') . $PropertyBooking->attachment)
            ) {
                @unlink(public_path('assets/img/attachments/properties/') . $PropertyBooking->attachment);
            }

            // second, delete the invoice
            if (
                !is_null($PropertyBooking->invoice) &&
                file_exists(public_path('assets/invoices/properties/') . $PropertyBooking->invoice)
            ) {
                @unlink(public_path('assets/invoices/properties/') . $PropertyBooking->invoice);
            }

            // finally, delete the property booking record from db
            $PropertyBooking->delete();
        }

        session()->flash('success', 'Property rental records deleted successfully!');

        /**
         * this 'success' is returning for ajax call.
         * if return == 'success' then ajax will reload the page.
         */
        return 'success';
    }


    private function generateInvoice($bookingInfo)
    {
        $fileName = $bookingInfo->booking_number . '.pdf';
        $directory = public_path('assets/invoices/properties/');

        if (!file_exists($directory)) {
            @mkdir($directory, 0775, true);
        }

        $fileLocated = $directory . $fileName;

        PDF::loadView('frontend.pdf.property_booking', compact('bookingInfo'))->save($fileLocated);

        return $fileName;
    }

    private function sendMailForPaymentStatus($PropertyBooking, $status)
    {
        // first get the mail template information from db
        if ($status == 1) {
            $mailTemplate = MailTemplate::where('mail_type', 'payment_received')->firstOrFail();
        } else {
            $mailTemplate = MailTemplate::where('mail_type', 'payment_cancelled')->firstOrFail();
        }
        $mailSubject = $mailTemplate->mail_subject;
        $mailBody = $mailTemplate->mail_body;

        // second get the website title & mail's smtp information from db
        $info = DB::table('basic_settings')
            ->select('website_title', 'smtp_status', 'smtp_host', 'smtp_port', 'encryption', 'smtp_username', 'smtp_password', 'from_mail', 'from_name')
            ->first();

        // replace template's curly-brace string with actual data
        $mailBody = str_replace('{customer_name}', $PropertyBooking->customer_name, $mailBody);
        $mailBody = str_replace('{website_title}', $info->website_title, $mailBody);

        // initialize a new mail
        $mail = new PHPMailer(true);

        // if smtp status == 1, then set some value for PHPMailer
        if ($info->smtp_status == 1) {
            $mail->isSMTP();
            $mail->Host       = $info->smtp_host;
            $mail->SMTPAuth   = true;
            $mail->Username   = $info->smtp_username;
            $mail->Password   = $info->smtp_password;

            if ($info->encryption == 'TLS') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }

            $mail->Port       = $info->smtp_port;
        }

        // finally add other informations and send the mail
        try {
            // Recipients
            $mail->setFrom($info->from_mail, $info->from_name);
            $mail->addAddress($PropertyBooking->customer_email);

            // Attachments (Invoice)
            $mail->addAttachment(public_path('assets/invoices/properties/') . $PropertyBooking->invoice);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $mailSubject;
            $mail->Body    = $mailBody;

            $mail->send();

            return;
        } catch (Exception $e) {
            return redirect()->back()->with('warning', 'Mail could not be sent!');
        }
    }

    public function amenities(Request $request)
    {
        // first, get the language info from db
        $language = Language::where('code', $request->language)->first();
        $information['language'] = $language;

        // then, get the property amenities of that language from db
        $information['amenities'] = PropertyAmenity::where('language_id', $language->id)
            ->orderBy('id', 'desc')
            ->get();

        // also, get all the languages from db
        $information['langs'] = Language::all();

        return view('backend.properties.amenities', $information);
    }

    public function storeAmenity(Request $request)
    {
        $rules = [
            'language_id' => 'required',
            'name' => 'required',
            'serial_number' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        $in = $request->all();

        PropertyAmenity::create($in);

        session()->flash('success', 'New property amenity added successfully!');

        return 'success';
    }

    public function updateAmenity(Request $request)
    {
        $rules = [
            'name' => 'required',
            'serial_number' => 'required'
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json([
                'errors' => $validator->getMessageBag()->toArray()
            ], 400);
        }

        PropertyAmenity::find($request->amenity_id)->update($request->all());

        session()->flash('success', 'Property amenity updated successfully!');

        return 'success';
    }

    public function deleteAmenity(Request $request)
    {
        PropertyAmenity::find($request->amenity_id)->delete();

        return redirect()->back()->with('success', 'Property amenity deleted successfully!');
    }

    public function bulkDeleteAmenity(Request $request)
    {
        $ids = $request->ids;

        foreach ($ids as $id) {
            PropertyAmenity::find($id)->delete();
        }

        session()->flash('success', 'Property amenities deleted successfully!');

        return 'success';
    }
}
