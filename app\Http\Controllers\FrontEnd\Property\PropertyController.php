<?php

namespace App\Http\Controllers\FrontEnd\Property;

use App\Http\Controllers\Controller;
use App\Models\PropertyManagement\Coupon;
use App\Models\PropertyManagement\Property;
use App\Models\PropertyManagement\PropertyBooking;
use App\Models\PropertyManagement\PropertyCategory;
use App\Models\PropertyManagement\PropertyContent;
use App\Models\PropertyManagement\PropertyLocation;
use App\Models\PropertyManagement\PropertyPlan;
use App\Models\PropertyManagement\PropertyReview;
use App\Models\PaymentGateway\OfflineGateway;
use App\Models\PaymentGateway\OnlineGateway;
use App\Models\RoomManagement\Room;
use App\Models\Vendor;
use App\Traits\MiscellaneousTrait;
use Carbon\Carbon;
use Config;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PropertyController extends Controller
{
  use MiscellaneousTrait;

  public function properties(Request $request)
  {
    $queryResult['breadcrumbInfo'] = MiscellaneousTrait::getBreadcrumb();
    $queryResult['propertyRating'] = DB::table('basic_settings')->select('property_rating_status')->first();

    $language = MiscellaneousTrait::getLanguage();

    $queryResult['categories'] = PropertyCategory::where('language_id', $language->id)->where('status', 1)->orderBy('serial_number', 'ASC')->get();

    $queryResult['pageHeading'] = MiscellaneousTrait::getPageHeading($language);

    $queryResult['maxPrice'] = Property::max('property_price');

    $queryResult['minPrice'] = Property::min('property_price');
    $queryResult['maxPersons'] = Property::max('max_persons');
    $queryResult['maxDays'] = Property::max('number_of_days');

    $property_name = $sort_value = $location_name = $min_price = $max_price = null;

    $category = $request->category;
    $min_persons = $request->min_persons;
    $max_persons = $request->max_persons;
    $min_days = $request->min_days;
    $max_days = $request->max_days;

    if ($request->filled('property_name')) {
      $property_name = $request->property_name;
    }
    if ($request->filled('sort_value')) {
      $sort_value = $request->sort_value;
    }
    if ($request->filled('location_name')) {
      $location_name = $request->location_name;
    }
    if ($request->filled('min_price')) {
      $min_price = $request->min_price;
    }
    if ($request->filled('max_price')) {
      $max_price = $request->max_price;
    }

    $propertyIds = [];

    if (!empty($property_name)) {
      $propertyContents = PropertyContent::where('language_id', $language->id)
        ->where('title', 'like', '%' . $property_name . '%')
        ->get();

      foreach ($propertyContents as $propertyContent) {
        if (!in_array($propertyContent->property_id, $propertyIds)) {
          array_push($propertyIds, $propertyContent->property_id);
        }
      }
    }

    $locationIds = [];

    if (!empty($location_name)) {
      $propertyLocations = PropertyLocation::where('language_id', $language->id)
        ->where('name', 'like', '%' . $location_name . '%')
        ->get();

      foreach ($propertyLocations as $propertyLocation) {
        if (!in_array($propertyLocation->property_id, $locationIds)) {
          array_push($locationIds, $propertyLocation->property_id);
        }
      }
    }

    $categoryIds = [];

    if (!empty($category)) {
      $propertyContents = PropertyContent::where('language_id', $language->id)
        ->where('property_category_id', $category)
        ->get();

      foreach ($propertyContents as $propertyContent) {
        if (!in_array($propertyContent->property_id, $categoryIds)) {
          array_push($categoryIds, $propertyContent->property_id);
        }
      }
    }

    $properties = Property::with([
      'property_content' => function ($query) use ($language) {
        return $query->where('language_id', $language->id);
      }
    ])
      ->when($property_name, function ($query) use ($propertyIds) {
        return $query->whereIn('id', $propertyIds);
      })
      ->when($location_name, function ($query) use ($locationIds) {
        return $query->whereIn('id', $locationIds);
      })
      ->when($category, function ($query) use ($categoryIds) {
        return $query->whereIn('id', $categoryIds);
      })
      ->when($min_price, function ($query, $min_price) {
        return $query->where('property_price', '>=', $min_price);
      })
      ->when($max_price, function ($query, $max_price) {
        return $query->where('property_price', '<=', $max_price);
      })
      ->when($min_persons, function ($query, $min_persons) {
        return $query->where('max_persons', '>=', $min_persons);
      })
      ->when($max_persons, function ($query, $max_persons) {
        return $query->where('max_persons', '<=', $max_persons);
      })
      ->when($min_days, function ($query, $min_days) {
        return $query->where('number_of_days', '>=', $min_days);
      })
      ->when($max_days, function ($query, $max_days) {
        return $query->where('number_of_days', '<=', $max_days);
      })
      ->when($sort_value, function ($query, $sort_value) {
        if ($sort_value == 'new') {
          return $query->orderBy('created_at', 'desc');
        } elseif ($sort_value == 'old') {
          return $query->orderBy('created_at', 'asc');
        } elseif ($sort_value == 'high-to-low') {
          return $query->orderBy('property_price', 'desc');
        } elseif ($sort_value == 'low-to-high') {
          return $query->orderBy('property_price', 'asc');
        }
      })
      ->orderBy('id', 'desc')
      ->paginate(9);

    $queryResult['properties'] = $properties;

    return view('frontend.properties.properties', $queryResult);
  }

  public function details($slug, $id)
  {
    $language = MiscellaneousTrait::getLanguage();

    $queryResult['details'] = Property::with([
      'propertyContent' => function ($query) use ($language) {
        return $query->where('language_id', $language->id);
      },
      'propertyLocationList' => function ($query) use ($language) {
        return $query->where('language_id', $language->id);
      },
      'propertyPlanList' => function ($query) use ($language) {
        return $query->where('language_id', $language->id);
      },
      'images',
      'propertyReview' => function ($query) {
        return $query->orderBy('id', 'desc');
      }
    ])->findOrFail($id);

    $queryResult['breadcrumbInfo'] = MiscellaneousTrait::getBreadcrumb();

    $queryResult['pageHeading'] = MiscellaneousTrait::getPageHeading($language);

    $queryResult['currencyInfo'] = MiscellaneousTrait::getCurrencyInfo();

    $queryResult['onlineGateways'] = OnlineGateway::where('status', 1)->get();

    $queryResult['offlineGateways'] = OfflineGateway::where('status', 1)->get();

    $queryResult['propertyRating'] = DB::table('basic_settings')->select('property_rating_status')->first();

    return view('frontend.properties.details', $queryResult);
  }

  public function applyCoupon(Request $request)
  {
    $coupon = Coupon::where('code', $request->coupon)->first();

    if (empty($coupon)) {
      return response()->json(['error' => 'Coupon not found!']);
    }

    if ($coupon->start_date > Carbon::now()->format('Y-m-d')) {
      return response()->json(['error' => 'Coupon is not valid yet!']);
    }

    if ($coupon->end_date < Carbon::now()->format('Y-m-d')) {
      return response()->json(['error' => 'Coupon has expired!']);
    }

    $propertyIds = json_decode($coupon->properties);

    if (!in_array($request->property_id, $propertyIds)) {
      return response()->json(['error' => 'Coupon is not valid for this property!']);
    }

    $property = Property::find($request->property_id);

    if ($coupon->type == 'fixed') {
      $discount = $coupon->value;
    } else {
      $discount = ($property->property_price * $coupon->value) / 100;
    }

    $total = $property->property_price - $discount;

    return response()->json([
      'discount' => $discount,
      'total' => $total
    ]);
  }

  // Legacy method names for backward compatibility
  public function properties(Request $request)
  {
    return $this->properties($request);
  }
}
