<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
  /**
   * The URIs that should be excluded from CSRF verification.
   *
   * @var array
   */
  protected $except = [
    '/room_booking/paytm/notify',
    '/room_booking/razorpay/notify',
    '/room_booking/flutterwave/notify',
    '/property_booking/paytm/notify',
    '/property_booking/razorpay/notify',
    '/property_booking/flutterwave/notify',
    '*/paytabs/notify',
    '*/phonepe/notify',
    '*iyzico/notify'
  ];
}
